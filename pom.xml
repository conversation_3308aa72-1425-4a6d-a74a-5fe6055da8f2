<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>net.jdair.operassist</groupId>
	<artifactId>jd-ms-operassist</artifactId>
	<version>1.0.0</version>
	<packaging>jar</packaging>

	<name>JD Microservice Flight Data</name>
	<description>JD运行辅助支持服务</description>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.6.3</version>
		<relativePath/>
	</parent>

	<properties>
		<common-io>2.6</common-io>
		<commons-fileupload>1.3.3</commons-fileupload>
		<commons-lang3>3.7</commons-lang3>
		<!-- 主要依赖库的版本定义 -->
		<gson.version>2.8.5</gson.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
	</properties>


	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>2021.0.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>


	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- spring cloud >>>-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
		</dependency>
		<!--<<< spring cloud-->

		<!-- feign -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-httpclient</artifactId>
		</dependency>
		<!-- feign -->

		<!--hutool工具类  文档可参考：https://apidoc.gitee.com/loolly/hutool/ -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.38</version>
		</dependency>
		<!--mybatisplus相关-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-generator</artifactId>
			<version>3.5.2</version>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>3.5.2</version>
		</dependency>
		<!--mybatisplus相关-->


		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
			<version>3.5.2</version>
		</dependency>

		<!-- mysql数据库驱动 -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<!-- druid数据库连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
			<version>1.1.22</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>1.1.22</version>
		</dependency>
		<!-- springboot测试 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!--日志工具-->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.7.21</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>${gson.version}</version>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>1.2.3</version>
		</dependency>

		<dependency>
			<groupId>net.logstash.logback</groupId>
			<artifactId>logstash-logback-encoder</artifactId>
			<version>7.0.1</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.4</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.54</version>
		</dependency>
		<!-- JWT -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-api</artifactId>
			<version>0.10.5</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-impl</artifactId>
			<version>0.10.5</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-jackson</artifactId>
			<version>0.10.5</version>
			<scope>runtime</scope>
		</dependency>
		<!--数据库密码加密工具-->
		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>3.0.3</version>
		</dependency>

		<!-- 高版本redis的lettuce需要commons-pool2 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
		</dependency>
		<!-- Spring Data Redis的启动器 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<!-- swagger2 >>>-->
		<!--swagger 接口文档插件-->
		<!-- https://mvnrepository.com/artifact/io.springfox/springfox-swagger2 -->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.9.2</version>
		</dependency>
		<!--swagger UI工具-->
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>swagger-bootstrap-ui</artifactId>
			<version>1.9.6</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.9.2</version>
		</dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-annotations</artifactId>
			<version>1.5.22</version>
		</dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-models</artifactId>
			<version>1.5.22</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/io.springfox/springfox-swagger-ui -->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>2.9.2</version>
		</dependency>
		<!--<<< swagger2 -->

		<!-- pagehelper 分页插件 -->
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
		</dependency>
		<!-- pagehelper 分页插件 -->
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<version>1.4.2</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<!-- commons begin-->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${common-io}</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>${commons-fileupload}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>${commons-lang3}</version>
		</dependency>
		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>
			<version>2.0.1</version>
		</dependency>

		<!-- esb 接口-->
		<dependency>
			<groupId>com.hnair</groupId>
			<artifactId>opcnet-api</artifactId>
			<version>2.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/opcnet-api-2.0-SNAPSHOT.jar</systemPath>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.hnair.opcnet</groupId>-->
<!--			<artifactId>opcnet-esb-client</artifactId>-->
<!--			<version>2.0-RELEASE</version>-->
<!--			<scope>system</scope>-->
<!--			<systemPath>${project.basedir}/libs/opcnet-esb-client-2.0-RELEASE.jar</systemPath>-->
<!--		</dependency>-->

<!--		<dependency>-->
<!--			<groupId>com.hnair.opcnet</groupId>-->
<!--			<artifactId>opcnet-api</artifactId>-->
<!--			<version>3.0</version>-->
<!--		</dependency>-->

		<dependency>
			<groupId>net.sourceforge</groupId>
			<artifactId>pinyin4j</artifactId>
			<version>2.5.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/pinyin4j-2.5.0.jar</systemPath>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.alibaba</groupId>-->
<!--			<artifactId>dubbo</artifactId>-->
<!--			<version>2.5.8</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.apache.zookeeper</groupId>-->
<!--			<artifactId>zookeeper</artifactId>-->
<!--			<version>3.4.6</version>-->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<artifactId>log4j</artifactId>-->
<!--					<groupId>log4j</groupId>-->
<!--				</exclusion>-->
<!--				<exclusion>-->
<!--					<artifactId>slf4j-log4j12</artifactId>-->
<!--					<groupId>org.slf4j</groupId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
<!--		</dependency>-->

<!--		<dependency>-->
<!--			<groupId>com.101tec</groupId>-->
<!--			<artifactId>zkclient</artifactId>-->
<!--			<version>0.8</version>-->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<artifactId>log4j</artifactId>-->
<!--					<groupId>log4j</groupId>-->
<!--				</exclusion>-->
<!--				<exclusion>-->
<!--					<artifactId>slf4j-log4j12</artifactId>-->
<!--					<groupId>org.slf4j</groupId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->

<!--		</dependency>-->
		<dependency>
			<groupId>com.caucho</groupId>
			<artifactId>hessian</artifactId>
			<version>4.0.7</version>
		</dependency>
		<!-- esb 接口  -->

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>log4j-over-slf4j</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.4</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>cn.zhxu</groupId>
			<artifactId>bean-searcher</artifactId>
			<version>4.2.7</version>
		</dependency>

		<dependency>
			<groupId>cn.zhxu</groupId>
			<artifactId>bean-searcher-boot-starter</artifactId>
			<version>4.2.7</version>
		</dependency>

		<dependency>
			<groupId>com.hnair.opcnet</groupId>
			<artifactId>opcnet-esb-client</artifactId>
			<version>4.4.2-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.hnair.opcnet</groupId>
			<artifactId>opcnet-esb-dubbo-wrapper</artifactId>
			<version>4.4.2-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.2</version> <!-- 或更高版本 -->
			<scope>test</scope>
		</dependency>


	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<includeSystemScope>true</includeSystemScope>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<repositories >
		<repository>
			<id>alimaven</id>
			<name>aliyun maven</name>
			<url>https://maven.aliyun.com/repository/central/</url>
			<!--			<layout>default</layout>-->
			<!--			<releases >-->
			<!--				<enabled>true</enabled >-->
			<!--			</releases >-->
			<!--			<snapshots >-->
			<!--				<enabled >false</enabled >-->
			<!--			</snapshots >-->
		</repository >

<!--		<repository>-->
<!--			<id>repo1</id>-->
<!--			<name>repo1</name>-->
<!--			<url>https://repo1.maven.org/maven2/</url>-->
<!--		</repository>-->

<!--		<repository>-->
<!--			<id>hnair-public2</id>-->
<!--			<name>hnair-public2</name>-->
<!--			<url>http://nexus.dev.hnair.net/repository/hnair-public/</url>-->
<!--		</repository>-->
		<repository>
			<id>haihangyun</id>
			<name>haihangyun-maven</name>
			<url>http://maven.haihangyun.com/content/groups/public/</url>
		</repository>
		<repository>
			<id>haihangyun-release</id>
			<name>haihangyun-maven</name>
			<url>http://maven.haihangyun.com/content/repositories/releases/</url>
		</repository>
		<repository>
			<id>haihangyun-snapshot</id>
			<name>haihangyun-maven</name>
			<url>http://maven.haihangyun.com/content/repositories/snapshots/</url>
		</repository>
		<repository>
			<id>10_121_31_83</id>
			<name>10_121_31_83_name</name>
			<url>http://10.121.31.83:9003/repository/maven-releases</url>
			<releases>
				<enabled>true</enabled>
			</releases>
		</repository>
		<repository>
			<id>10_121_31_83_2</id>
			<name>10_121_31_83_name_2</name>
			<url>http://10.121.31.83:9003/repository/maven-snapshots</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>

	</repositories>
</project>
