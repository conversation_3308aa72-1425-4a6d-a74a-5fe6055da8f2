# 开发环境配置文件
server:
  port: 8085     # Springboot占用端口

spring:
  application:
    name: jd-ms-operassist
  autoconfigure:
    #自动化配置 例外处理
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    dynamic:
      primary: dwh #默认数据源
      datasource:
        dwh:
          driver-class-name: com.mysql.cj.jdbc.Driver     # 数据库驱动
          url: *************************************************************************************************************************************************************************************     # 数据库链接
          username: ENC(X1DvmkguxhnAVHbMYn2WXsEnOFF9Gt+2)
          password: ENC(tVlIV8HGix59fUDv6EsPkjGQFqBHWuNgEd6rrOh59jc=)
      durid:
        initial-size: 5
        min-idle: 5
        max-active: 20
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20

  redis:
    password: ENC(3GUCDWEPbYoWzHCFGfplTFoa6+NLiciwcXEzRrq+O6k=)
    port: 6379
    #线上环境
    host: ************
    lettuce:
      pool:
        # 最大空闲连接数 默认8
        max-idle: 8
        # 最小空闲连接数 默认0
        min-idle: 0
        # 最大活跃链接数 默认8
        max-active: 8
    #请求时间
    timeout: 5000

  devtools:
    restart:
      enabled: true     # 开启热更新

# logback 配置
logging:
  config: classpath:log-back.xml
  file:
    path: /mnt/app/logs/jd-ms-operassist/
  level:
    root: INFO

# 股份 esb 接口配置信息
esb:
  dubbo:
    registry:
      address: odsreg-ns.hnair.net:2181
      protocol: zookeeper
    application:
      name: ffp
      owner: opcnet
    protocol:
      port: 20880

# eureka 注册中心地址
eureka:
  client:
    serviceUrl:
      defaultZone: http://************:8761/eureka/,http://************:8761/eureka/
  instance:
    prefer-ip-address: true
bean-searcher:
  params:
    pagination:
      # 起始页，不配置默认为0，这里配置为1，是为了兼容element UI的分页组件
      start: 1
  sql:
    dialect: MySQL
  field-convertor:
    date-formats:
      # 只对MapSearcher有效
      net.jdair.operassist.bizz.flightrisk.domain.DutyRosterV2: yyyy-MM-dd HH:mm:ss

odsapi:
  url: https://odsapi-bs.hnair.net/api
  serialization: fastjson2
  timeout: 600000