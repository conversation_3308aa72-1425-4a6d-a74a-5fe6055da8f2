# 基础配置文件
spring:
  profiles:
    active: prd     # 配置文件环境
  http:
    encoding:
      force: true
      charset: utf-8
      enabled: true
  mvc:
    throw-exception-if-no-handler-found: true     # 接口404时抛出异常
    view:
      prefix: /WEB-INF/pages/
      suffix: .jsp
      favicon:
        enabled: false
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  resources:
    add-mappings: false
    static-locations: classpath:/static/,classpath:/META-INF/resources/,classpath:/META-INF/resources/webjars/

server:
  servlet:
    context-path: /    # 应用路径
  tomcat:
    uri-encoding: UTF-8

jasypt:
  encryptor:
    # 加密（需要替换成你自己需要的秘钥）
    # 增加命令行启动参数
    # 指定加密方式
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator

#---------mybatis------------------------
mybatis-plus:
  type-aliases-package: net.jdair.flightdata.bizz.**.domain
  mapper-locations: classpath:/mapper/**/*Mapper.xml

# -------- feign 配置 -------------------
feign:
  client:
    config:
      default:
        # 设置通用client连接超时为100毫秒，连接时间不宜过长，防止依赖服务负载过高情况下活跃连接都在长时间尝试建立连接，建议设置比较短以便快速失败
        connectTimeout: 100

        # 设置通用client响应超时为1秒，单个接口响应时间不宜过长，建议为1秒，超过1秒的一般都需要优化接口，如果无法优化建议走独立配置
        readTimeout: 5000

  httpclient:
    # 设置连接存活时间为900秒，超过该时间后空闲连接会被回收，注意的是如果你通过Java Config覆盖默认ApacheHttpClient，一定要创建定时器来检测无用连接
    timeToLive: 900

    # 设置全局最大连接数为300个连接，可根据具体有多少FeignClient来决定，比如一个HOST最多50个连接，一个有8个HOST，每个HOST调用频率有高有低，可折中取值300
    max-connections: 300

    # 设置单个HOST最大连接数为50个，可根据高峰期调用频率来调整
    maxConnectionsPerRoute: 50

    ##################################################################################################
    # 单节点下n秒钟可发起的最大【依赖接口调用次数】计算方式如下：
    #   最大连接数 * （n * 1000 / 接口平均响应时间（ms） ）= n秒钟内最大接口调用能力
    #
    # 以上述配置为例，最差情况下接口平均响应时间为（readTimeout + connectTimeout）ms，1s内接口最大调用次数为：
    #   300 * ( 1 * 1000 / 1100 ) = 272 (次)
    #
    # 针对单HOST最大接口调用次数为：
    #   50 * ( 1 * 1000 / 1100 ) = 45 (次)
    #
    # 全局最大连接数要根据HOST总数、单HOST最大连接数、调用频率来调整，一般取折中值即可。
    #   全局最大连接数 <= 单个HOST最大连接数 * HOST总数 <= tomcat最大连接数
    ##################################################################################################

# -------- feign 配置 -------------------