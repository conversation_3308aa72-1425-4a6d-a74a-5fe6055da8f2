<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.operassist.bizz.flightrisk.mapper.OdsRosterMapper">

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="odsRosterList.id"
            keyColumn="id">
        insert into flt_ods_roster(
        `fltDate`,
        `fltIds`,
        `acNo`,
        `depTimes`,
        `routes`,
        `maintainStn`,
        `cnRoutes`,
        `stcs`,
        `flyTimes`,
        `accd`,
        `captain`,
        `captain<PERSON><PERSON><PERSON><PERSON><PERSON>`,
        `pilot<PERSON>hecker`,
        `pilotChecker<PERSON>taffNo`,
        `inspectedPilot`,
        `inspectedPilotStaffNo`,
        `checkType`,
        `skipper1`,
        `skipper1<PERSON>taffNo`,
        `skipper1<PERSON><PERSON>rk`,
        `firstVice1`,
        `firstVice1StaffNo`,
        `firstVice1Remark`,
        `accompany1`,
        `accompany1StaffNo`,
        `pilotDeadheadOps`,
        `pilotDeadheadOpsStaffNo`,
        `pilotDeadheadOther`,
        `pilotDeadheadOtherStaffNo`,
        `chiefStewardCaptain`,
        `chiefStewardCaptainStaffNo`,
        `chiefStewardCaptainRemark`,
        `stewardCap1`,
        `stewardCap1StaffNo`,
        `stewardCap1Remark`,
        `safer1`,
        `stewards`,
        `stewardsStaffNo`,
        `stewardChecker`,
        `stewardCheckerStaffNo`,
        `stewardInstructor`,
        `stewardInstructorStaffNo`,
        `stewardCheckType`,
        `stewardDeadheadOps`,
        `stewardDeadheadOpsStaffNo`,
        `stewardDeadheadOther`,
        `stewardDeadheadOtherStaffNo`,
        `jumpSeating`,
        `jumpSeatingStaffNo`,
        `crwPilotInf`,
        `crwStewardInf`,
        `std`,
        `safer1StaffNo`,
        `captainRemark`,
        `companyCode`,
        `companyName`,
        `companyId`,
        `signatureDate`,
        `signatureImageUrl`,
        `arrTimes`,
        `dispatcher`,
        `engineer`,
        `stdFirst`,
        `cruisingCaptain`,
        `cruisingCaptains`,
        `updatedTime`,
        `jumpSeating1`,
        `flightId`,
        `reportId`,
        `gmt_create`,
        `gmt_modified`
        ) values
        <foreach collection="odsRosterList" item="item" index="index" separator=",">
            (
            #{item.fltDate},
            #{item.fltIds},
            #{item.acNo},
            #{item.depTimes},
            #{item.routes},
            #{item.maintainStn},
            #{item.cnRoutes},
            #{item.stcs},
            #{item.flyTimes},
            #{item.accd},
            #{item.captain},
            #{item.captainStaffNo},
            #{item.pilotChecker},
            #{item.pilotCheckerStaffNo},
            #{item.inspectedPilot},
            #{item.inspectedPilotStaffNo},
            #{item.checkType},
            #{item.skipper1},
            #{item.skipper1StaffNo},
            #{item.skipper1Remark},
            #{item.firstVice1},
            #{item.firstVice1StaffNo},
            #{item.firstVice1Remark},
            #{item.accompany1},
            #{item.accompany1StaffNo},
            #{item.pilotDeadheadOps},
            #{item.pilotDeadheadOpsStaffNo},
            #{item.pilotDeadheadOther},
            #{item.pilotDeadheadOtherStaffNo},
            #{item.chiefStewardCaptain},
            #{item.chiefStewardCaptainStaffNo},
            #{item.chiefStewardCaptainRemark},
            #{item.stewardCap1},
            #{item.stewardCap1StaffNo},
            #{item.stewardCap1Remark},
            #{item.safer1},
            #{item.stewards},
            #{item.stewardsStaffNo},
            #{item.stewardChecker},
            #{item.stewardCheckerStaffNo},
            #{item.stewardInstructor},
            #{item.stewardInstructorStaffNo},
            #{item.stewardCheckType},
            #{item.stewardDeadheadOps},
            #{item.stewardDeadheadOpsStaffNo},
            #{item.stewardDeadheadOther},
            #{item.stewardDeadheadOtherStaffNo},
            #{item.jumpSeating},
            #{item.jumpSeatingStaffNo},
            #{item.crwPilotInf},
            #{item.crwStewardInf},
            #{item.std},
            #{item.safer1StaffNo},
            #{item.captainRemark},
            #{item.companyCode},
            #{item.companyName},
            #{item.companyId},
            #{item.signatureDate},
            #{item.signatureImageUrl},
            #{item.arrTimes},
            #{item.dispatcher},
            #{item.engineer},
            #{item.stdFirst},
            #{item.cruisingCaptain},
            #{item.cruisingCaptains},
            #{item.updatedTime},
            #{item.jumpSeating1},
            #{item.flightId},
            #{item.reportId},
            #{item.gmtCreate},
            #{item.gmtModified}
            )
        </foreach>
        ON duplicate KEY UPDATE
        fltDate = values(fltDate),
        fltIds = values(fltIds),
        acNo = values(acNo),
        depTimes = values(depTimes),
        routes = values(routes),
        maintainStn = values(maintainStn),
        cnRoutes = values(cnRoutes),
        stcs = values(stcs),
        flyTimes = values(flyTimes),
        accd = values(accd),
        captain = values(captain),
        captainStaffNo = values(captainStaffNo),
        pilotChecker = values(pilotChecker),
        pilotCheckerStaffNo = values(pilotCheckerStaffNo),
        inspectedPilot = values(inspectedPilot),
        inspectedPilotStaffNo = values(inspectedPilotStaffNo),
        checkType = values(checkType),
        skipper1 = values(skipper1),
        skipper1StaffNo = values(skipper1StaffNo),
        skipper1Remark = values(skipper1Remark),
        firstVice1 = values(firstVice1),
        firstVice1StaffNo = values(firstVice1StaffNo),
        firstVice1Remark = values(firstVice1Remark),
        accompany1 = values(accompany1),
        accompany1StaffNo = values(accompany1StaffNo),
        pilotDeadheadOps = values(pilotDeadheadOps),
        pilotDeadheadOpsStaffNo = values(pilotDeadheadOpsStaffNo),
        pilotDeadheadOther = values(pilotDeadheadOther),
        pilotDeadheadOtherStaffNo = values(pilotDeadheadOtherStaffNo),
        chiefStewardCaptain = values(chiefStewardCaptain),
        chiefStewardCaptainStaffNo = values(chiefStewardCaptainStaffNo),
        chiefStewardCaptainRemark = values(chiefStewardCaptainRemark),
        stewardCap1 = values(stewardCap1),
        stewardCap1StaffNo = values(stewardCap1StaffNo),
        stewardCap1Remark = values(stewardCap1Remark),
        safer1 = values(safer1),
        stewards = values(stewards),
        stewardsStaffNo = values(stewardsStaffNo),
        stewardChecker = values(stewardChecker),
        stewardCheckerStaffNo = values(stewardCheckerStaffNo),
        stewardInstructor = values(stewardInstructor),
        stewardInstructorStaffNo = values(stewardInstructorStaffNo),
        stewardCheckType = values(stewardCheckType),
        stewardDeadheadOps = values(stewardDeadheadOps),
        stewardDeadheadOpsStaffNo = values(stewardDeadheadOpsStaffNo),
        stewardDeadheadOther = values(stewardDeadheadOther),
        stewardDeadheadOtherStaffNo = values(stewardDeadheadOtherStaffNo),
        jumpSeating = values(jumpSeating),
        jumpSeatingStaffNo = values(jumpSeatingStaffNo),
        crwPilotInf = values(crwPilotInf),
        crwStewardInf = values(crwStewardInf),
        std = values(std),
        safer1StaffNo = values(safer1StaffNo),
        captainRemark = values(captainRemark),
        companyCode = values(companyCode),
        companyName = values(companyName),
        companyId = values(companyId),
        signatureDate = values(signatureDate),
        signatureImageUrl = values(signatureImageUrl),
        arrTimes = values(arrTimes),
        dispatcher = values(dispatcher),
        engineer = values(engineer),
        stdFirst = values(stdFirst),
        cruisingCaptain = values(cruisingCaptain),
        cruisingCaptains = values(cruisingCaptains),
        updatedTime = values(updatedTime),
        jumpSeating1 = values(jumpSeating1),
        flightId = values(flightId),
        reportId = values(reportId),
        gmt_modified = values(gmt_modified)
    </insert>

</mapper>
