<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="CONSOLE_LOG_PATTERN" value="%date [%thread] %-5level %logger{80} - %msg%n" />

    <!-- 日志存放路径 -->
    <property name="LOG_PATH" value="/mnt/app/logs/jd-ms-operassist/" />

    <include
            resource="org/springframework/boot/logging/logback/console-appender.xml" />

    <appender name="FILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/all.%d{yyyy-MM-dd}.log
            </fileNamePattern>
            <!-- 最多 120 个文件 -->
            <maxHistory>120</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%date [%thread] %-5level %logger{80} - %msg%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>

    <appender name="FILE-WARN"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/warn.%d{yyyy-MM-dd}.log
            </fileNamePattern>
            <maxHistory>120</maxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>

        <encoder>
            <pattern>%date [%thread] %-5level %logger{80} - %msg%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>

    <appender name="FILE-ERROR"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/error.%d{yyyy-MM-dd}.log
            </fileNamePattern>
            <maxHistory>120</maxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>

        <encoder>
            <pattern>%date [%thread] %-5level %logger{80} - %msg%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>

    <appender name="AUDITLOG"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/audit.%d{yyyy-MM-dd}.log
            </fileNamePattern>
            <maxHistory>120</maxHistory>
        </rollingPolicy>

<!--        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">-->
<!--            <providers>-->
<!--                <pattern>-->
<!--                    <pattern>-->
<!--                        {-->
<!--                            "timestamp": "%date{yyyy-MM-dd HH:mm:ss.SSS}",-->
<!--                            "message": "%message"-->
<!--                        }-->
<!--                    </pattern>-->
<!--                </pattern>-->
<!--            </providers>-->
<!--        </encoder>-->

        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <fieldNames>
                <version>[ignore]</version>
                <timestamp>[ignore]</timestamp>
                <logger>[ignore]</logger>
                <thread>[ignore]</thread>
                <level>[ignore]</level>
                <levelValue>[ignore]</levelValue>
                <message>[ignore]</message>
            </fieldNames>
        </encoder>

    </appender>

    <!--logback.LogbackDemo：类的全路径 -->
    <logger name="net.jdair.operassist.common.log.AuditLogger" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="AUDITLOG" />
    </logger>

    <root>
        <level value="${logging.level.root}" />
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="FILE-WARN" />
        <appender-ref ref="FILE-ERROR" />
    </root>
</configuration>

