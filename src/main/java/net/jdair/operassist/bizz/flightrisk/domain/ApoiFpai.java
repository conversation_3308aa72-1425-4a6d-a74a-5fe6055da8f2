package net.jdair.operassist.bizz.flightrisk.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("机场到港航班信息")
@Data
@TableName("flt_operassist_api_apoiFpai")
public class ApoiFpai {
    @ApiModelProperty("ODS主键")
    @TableField("id")
    @TableId
    private Long id;//ODS主键
    @ApiModelProperty("源表ID主键")
    @TableField("csdId")
    private Long csdId;//源表ID主键
    @ApiModelProperty("CSD_MSG_XML表ID主键")
    @TableField("srcId")
    private Long srcId;//CSD_MSG_XML表ID主键
    @ApiModelProperty("航空器识别标志")
    @TableField("callsign")
    private String callsign;//航空器识别标志
    @ApiModelProperty("航班号")
    @TableField("flightNo")
    private String flightNo;//航班号
    @ApiModelProperty("航班日期-北京")
    @TableField("datopChn")
    private String datopChn;//航班日期-北京
    @ApiModelProperty("航班唯一标识符")
    @TableField("gufi")
    private String gufi;//航班唯一标识符
    @ApiModelProperty("航空器注册号")
    @TableField("regnumber")
    private String regnumber;//航空器注册号
    @ApiModelProperty("航空器地址码")
    @TableField("addresscode")
    private String addresscode;//航空器地址码
    @ApiModelProperty("计划离港时间")
    @TableField("sobt")
    private String sobt;//计划离港时间
    @ApiModelProperty("计划到港时间")
    @TableField("sibt")
    private String sibt;//计划到港时间
    @ApiModelProperty("计划起飞机场")
    @TableField("depap")
    private String depap;//计划起飞机场
    @ApiModelProperty("计划目的地机场")
    @TableField("arrap")
    private String arrap;//计划目的地机场
    @ApiModelProperty("计划起飞机场(三字码)")
    @TableField("depapT")
    private String depapT;//计划起飞机场(三字码)
    @ApiModelProperty("计划目的地机场(三字码)")
    @TableField("arrapT")
    private String arrapT;//计划目的地机场(三字码)
    @ApiModelProperty("预计撤轮档时间")
    @TableField("eobt")
    private String eobt;//预计撤轮档时间
    @ApiModelProperty("到港客梯车对接时间")
    @TableField("arls")
    private String arls;//到港客梯车对接时间
    @ApiModelProperty("到港摆渡车到位时间")
    @TableField("arrshuttlereadytime")
    private Date arrshuttlereadytime;//到港摆渡车到位时间
    @ApiModelProperty("靠桥时间")
    @TableField("abip")
    private String abip;//靠桥时间
    @ApiModelProperty("开始下客时间")
    @TableField("startunboardtime")
    private Date startunboardtime;//开始下客时间
    @ApiModelProperty("完成下客时间")
    @TableField("endunboardtime")
    private Date endunboardtime;//完成下客时间
    @ApiModelProperty("到港航班地面移交时间")
    @TableField("arrgroundhandovertime")
    private String arrgroundhandovertime;//到港航班地面移交时间
    @ApiModelProperty("实际到港时间")
    @TableField("aibt")
    private String aibt;//实际到港时间
    @ApiModelProperty("到港航班停机位")
    @TableField("arrstand")
    private String arrstand;//到港航班停机位
    @ApiModelProperty("预计到港行李转盘")
    @TableField("expectedarrivalluggageturntable")
    private String expectedarrivalluggageturntable;//预计到港行李转盘
    @ApiModelProperty("来源系统")
    @TableField("sourcesystemId")
    private String sourcesystemId;//来源系统
    @ApiModelProperty("发送时间")
    @TableField("sendTime")
    private String sendTime;//发送时间
    @ApiModelProperty("创建时间")
    @TableField("srcCreatedTime")
    private Date srcCreatedTime;//创建时间
    @ApiModelProperty("更新时间")
    @TableField("srcUpdatedTime")
    private Date srcUpdatedTime;//更新时间
    @ApiModelProperty("deleted")
    @TableField("deleted")
    private Integer deleted;//deleted
    @ApiModelProperty("createdTime")
    @TableField("createdTime")
    private Date createdTime;//createdTime
    @ApiModelProperty("updatedTime")
    @TableField("updatedTime")
    private Date updatedTime;//updatedTime
    @ApiModelProperty("batchJobNo")
    @TableField("batchJobNo")
    private String batchJobNo;//batchJobNo
    @ApiModelProperty("最早原计划离岗时间")
    @TableField("srcStd")
    private String srcStd;//最早原计划离岗时间
    @ApiModelProperty("发送时间")
    @TableField("arSendTime")
    private String arSendTime;//发送时间
    @ApiModelProperty("ODS更新时间")
    @TableField("maxUpdatedTime")
    private String maxUpdatedTime;//ODS更新时间




}
