package net.jdair.operassist.bizz.flightrisk.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.domain.FltFocStandardInfo;
import net.jdair.operassist.bizz.flightrisk.mapper.FltFocStandardInfoMapper;
import net.jdair.operassist.bizz.flightrisk.service.FltFocStandardInfoService;
import net.jdair.operassist.bizz.flightrisk.vo.FltFocStandInfoSearchVo;
import net.jdair.operassist.common.util.LocalDateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <p>
 * foc航班动态信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-02
 */
@Slf4j
@Service
public class FltFocStandardInfoServiceImpl extends ServiceImpl<FltFocStandardInfoMapper, FltFocStandardInfo> implements FltFocStandardInfoService {

    @Autowired
    private FltFocStandardInfoMapper standardInfoMapper;

    @Override
    public Page<FltFocStandardInfo> listFltFocStandardInfosByPage(FltFocStandInfoSearchVo param) {
        log.debug("正在执行分页查询fltFocStandardInfo: page = {} pageSize = {} factor = {}", param.getPage(), param.getPageSize(), JSON.toJSONString(param));
        LambdaQueryWrapper<FltFocStandardInfo> queryWrapper = new LambdaQueryWrapper<>();
        //设置查询条件
        if (StringUtils.isNotBlank(param.getFlightNo())) {  // 航班号
            queryWrapper.like(FltFocStandardInfo::getFlightNo, param.getFlightNo());
        }
        if (StringUtils.isNotBlank(param.getDepStn())) {  // 出发地三字码
            queryWrapper.like(FltFocStandardInfo::getDepStn, param.getDepStn());
        }
        if (StringUtils.isNotBlank(param.getArrStn())) {  // 目的地三字码
            queryWrapper.like(FltFocStandardInfo::getArrStn, param.getArrStn());
        }
        if (param.getFlightDateStart() != null) {
            String startDate = param.getFlightDateStart();
            queryWrapper.apply("date_format(std_local,'%Y-%m-%d') >= {0}", startDate);
        }
        if (param.getFlightDateEnd() != null) {
            String endDate = param.getFlightDateEnd();
            queryWrapper.apply("date_format(std_local,'%Y-%m-%d') <= {0}", endDate);
        }
        queryWrapper.orderByDesc(FltFocStandardInfo::getId);
        //查询数据
        Page<FltFocStandardInfo> result = super.page(new Page<>(param.getPage(), param.getPageSize()), queryWrapper);
        log.debug("分页查询fltFocStandardInfo完毕: 结果数 = {} ", result.getRecords().size());
        return result;
    }

    @Override
    public List<FltFocStandardInfo> listMq() {
        log.debug("正在执行分页查询listMq");
        LambdaQueryWrapper<FltFocStandardInfo> queryWrapper = new LambdaQueryWrapper<>();
        //航班状态：ARR 落地 NDR落地 ATA到达
        String inSql = "'ARR','NDR','ATA'";
        queryWrapper.inSql(FltFocStandardInfo :: getStatus , inSql);
        //开始时间  取整点时间（推送mq的定时任务要比同步航班动态的定时任务延迟几分钟，防止取不到数据）
        LocalDateTime now = LocalDateTime.now();

        int minute = now.getMinute();
        if (minute > 30){
            //按照半点作为查询条件
            now = LocalDateTime.of(now.getYear(),now.getMonth(),now.getDayOfMonth(),now.getHour(),30,0);
        }else{
            //按照整点作为查询条件
            now = LocalDateTime.of(now.getYear(),now.getMonth(),now.getDayOfMonth(),now.getHour(),0,0);
        }

        LocalDateTime localDateTime = now.minusMinutes(30L);
        String start = localDateTime.format(DateTimeFormatter.ofPattern(LocalDateUtils.YYYY_MM_DD_HH_MM_SS));
        String end = now.format(DateTimeFormatter.ofPattern(LocalDateUtils.YYYY_MM_DD_HH_MM_SS));
        //时间范围
        queryWrapper.between(FltFocStandardInfo::getAtaChn ,start , end);
        //航班日期：当日日期
        String format = now.format(DateTimeFormatter.ofPattern(LocalDateUtils.YYYY_MM_DD));
        queryWrapper.eq(FltFocStandardInfo::getDatopLocal , format);
        return standardInfoMapper.selectList(queryWrapper);
    }
}
