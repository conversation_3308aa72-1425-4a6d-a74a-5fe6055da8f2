package net.jdair.operassist.bizz.flightrisk.controller;


import com.alibaba.fastjson.JSONArray;
import com.hnair.opcnet.api.ods.ap.AirportInfoPageRequest;
import com.hnair.opcnet.api.ods.ap.AirportInfoPageResponse;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.common.esbapi.ESBService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Api(value = "ApAirportApi 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/ApAirportApi")
public class ApAirportApiController {


    @Resource
    private ESBService esbService;

    @ApiOperation(value = "查询 napsApi_findCompanyRoutesByPage 数据",
            notes = "查询 napsApi_findCompanyRoutesByPage 数据")
    @RequestMapping(value = "/getAirportInfoByPage")
    private AirportInfoPageResponse getAirportInfoByPage(@RequestParam Map<String, Object> options , AirportInfoPageRequest airportInfoPageRequest) {
        Integer pageIndex =0 ;
        try{
            if(airportInfoPageRequest.getPageParam()!=null){
                pageIndex = airportInfoPageRequest.getPageParam().getPageIndex();
            };
            //airportInfoPageRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }
        // log.info("同步航班动态数据查询接口(第{}页)，开始", pageIndex);

        log.info("ESB查询esbService.napsApi_findCompanyRoutesByPage  接口，请求：{}", JSONArray.toJSONString(airportInfoPageRequest));
        long start = System.currentTimeMillis();
        AirportInfoPageResponse airportInfoPageResponse = esbService.getAirportInfoByPage(airportInfoPageRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询esbService.napsApi_findCompanyRoutesByPage 接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(airportInfoPageResponse), end - start);
        return airportInfoPageResponse;
    }

}
