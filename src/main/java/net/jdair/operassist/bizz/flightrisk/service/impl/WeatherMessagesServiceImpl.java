package net.jdair.operassist.bizz.flightrisk.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.domain.MNfmsFmalFcti;
import net.jdair.operassist.bizz.flightrisk.domain.WeatherMessages;
import net.jdair.operassist.bizz.flightrisk.service.MNfmsFmalFctiService;
import net.jdair.operassist.bizz.flightrisk.service.WeatherMessagesService;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class WeatherMessagesServiceImpl implements WeatherMessagesService {


    @Override
    public void loadEsbDataToLocalDb(String days) throws Exception {

    }

    @Override
    public Page<WeatherMessages> findWeatherMessagesByPage_cache(Map<String, Object> options, Page page) {



        return null;
    }
}
