package net.jdair.operassist.bizz.flightrisk.controller;

import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.common.esbapi.ESBService;
import net.jdair.operassist.common.response.RestResponse;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * EFB查询机组
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/operassist/efbFlight")
@RequiredArgsConstructor
public class EfbFlightApiController {

    private final ESBService esbService;

    /**
     * 从ods查询efb机组信息
     *
     * @param options 请求
     * @return 结果
     */
    @RequestMapping(value = "/getEfbFlightLogLegsCrew")
    public RestResponse getEfbFlightLogLegsCrew(@RequestParam Map<String, Object> options ) {
        try {
            ApiRequest apiRequest = new ApiRequest();
            apiRequest.getOptions().putAll(options);
            ApiResponse apiResponse = esbService.getEfbFlightLogLegsCrew(apiRequest);
            return RestResponse.ok(apiResponse);
        }catch (Exception e){
            log.error("ESB查询航班getEfbFlightLogLegsCrew接口，异常：{}",e.getMessage(),e);
        }
        return RestResponse.exception("查询失败");
    }


}
