package net.jdair.operassist.bizz.flightrisk.controller;


import com.alibaba.fastjson.JSONArray;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.common.esbapi.ESBService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Api(value = "AircraftGaultInfo 分页获取通告影响公司航路数据信息接口")
@Slf4j
@RestController
@RequestMapping("/operassist/CifApi")
public class CifApiController {

    @Resource
    private ESBService esbService;

    /**
     * 分页获取通告影响公司航路数据信息接口
     * ***/
    @ApiOperation(value = "查询 CifApi_getMCifEffectrouteByPage 数据",
                  notes = "查询 CifApi_getMCifEffectrouteByPage 数据")
    @RequestMapping(value = "/getMCifEffectrouteByPage")
    private ApiResponse getMCifEffectrouteByPage(@RequestParam Map<String, Object> options ,
                                                 ApiRequest apiRequest) {
        Integer pageIndex =0 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }

        log.info("ESB查询esbService.cifApi_getMCifEffectrouteByPage  接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = esbService.cifApi_getMCifEffectrouteByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询esbService.cifApi_getMCifEffectrouteByPage 接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }


}
