package net.jdair.operassist.bizz.flightrisk.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import net.jdair.operassist.common.response.esb.EngDeDefer;

import java.io.Serializable;
import java.util.Date;

/**
 *
 */
@Data
public class EngDeDeferSearchVo implements Serializable {

    @ApiModelProperty(value = "页码。如: 1")
    private Integer page;

    @ApiModelProperty(value = "每页数量。 如：10")
    private Integer pageSize;

    // 运行限制	fllimited	String	是否可为空:Y(备注:例：X)
    private String fllimited;

    // 航班号,多个逗号分隔	flightNos	String	是否可为空:Y(备注:例：HU7801,HU7802)
    private String flightNos;

    // 起飞机场三字码	depStn	String	是否可为空:Y(备注:例：PEK)
    private String depStn;

    // 到达机场三字码	arrStn	String	是否可为空:Y(备注:例：HAK)
    private String arrStn;

    // 长机号,多个逗号分隔	longNos	String	是否可为空:Y(备注:例：B2723,B2725)
    private String longNos;

    // 保留依据(1-是,0-否)	mel	String	是否可为空:Y(备注:例：1-是)
    private String mel;

    // 座位限制(不为空则查询DISABLE_SEAT=X OR LIMIT_SEAT=X)	disableAndLimitSeat	String	是否可为空:Y(备注:例：X)
    private String disableAndLimitSeat;

    // 是否座椅禁用,是标X	disableSeat	String	是否可为空:Y(备注:例：X)
    private String disableSeat;

    // 是否座椅限制,是标X	limitSeat	String	是否可为空:Y(备注:例：X)
    private String limitSeat;

    // DD单更新日期开始>=	updatedTimeStart	String	格式：yyyy-MM-dd HH:mm:ss
    private String updatedTimeStart;

    // DD单更新日期结束<=	updatedTimeEnd 	String	格式：yyyy-MM-dd HH:mm:ss
    private String updatedTimeEnd;

    // 是否关闭	closed	Boolean	如：true/false
    private String closed;

    // std开始(输入北京时间)>=,不传则默认当日范围	stdStart	String	格式：yyyy-MM-dd HH:mm:ss
    private String stdStart;

    // std结束(输入北京时间)<=,不传则默认当日范围	stdEnd 	String	格式：yyyy-MM-dd HH:mm:ss
    private String stdEnd;

    // 航司三字码	station	String
    private String station;

}
