package net.jdair.operassist.bizz.flightrisk.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.jdair.operassist.bizz.flightrisk.domain.DutyRosterV2;
import org.springframework.transaction.annotation.Transactional;

public interface DutyRosterV2Service {

    @Transactional
        //加载昨日数据到数据库
    int   loadEsbDataToLocalDb_dutyRosterV2(String date);

    public IPage<DutyRosterV2> getDutyRosterV2_db(Page<DutyRosterV2> resultPage, QueryWrapper<DutyRosterV2> queryp);

}
