package net.jdair.operassist.bizz.flightrisk.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 数据库对象
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "flt_ods_roster")
public class OdsRoster extends Model<OdsRoster> {

    /**
     * 主键
     */
    @TableField("id")
    private Long id;

    /**
     * 航班日期 只包含首段的日期和时间,在还没有实际时间时，DATOP是计划时间，如有，那DATOP就是实际时间(北京时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @TableField("fltDate")
    private Date fltDate;

    /**
     * 航班号  数据可能为多个，以/分隔
     */
    @TableField("fltIds")
    private String fltIds;

    /**
     * 飞机号  数据可能为多个，以/分隔
     */
    @TableField("acNo")
    private String acNo;

    /**
     * 起飞日期
     */
    @TableField("depTimes")
    private String depTimes;

    /**
     * 航线  数据可能为多个，以/分隔
     */
    @TableField("routes")
    private String routes;

    /**
     * 报务教员
     */
    @TableField("maintainStn")
    private String maintainStn;

    /**
     * 备注
     */
    @TableField("cnRoutes")
    private String cnRoutes;

    /**
     * 航班性质  数据可能为多个，以/分隔
     */
    @TableField("stcs")
    private String stcs;

    /**
     * 飞行小时
     */
    @TableField("flyTimes")
    private String flyTimes;

    /**
     * 机型  数据可能为多个，以/分隔
     */
    @TableField("accd")
    private String accd;

    /**
     * 机长  数据可能为多个，以@分隔
     */
    @TableField("captain")
    private String captain;

    /**
     * 机长员工编号  数据可能为多个，以@分隔
     */
    @TableField("captainStaffNo")
    private String captainStaffNo;

    /**
     * 检查员 （飞行）  数据可能为多个，以/或、分隔
     */
    @TableField("pilotChecker")
    private String pilotChecker;
    /**
     * 检查员员工编号 （飞行）  数据可能为多个，以/分隔。有0值
     */
    @TableField("pilotCheckerStaffNo")
    private String pilotCheckerStaffNo;

    /**
     * 学员 （飞行）  数据可能为多个，以/分隔
     */
    @TableField("inspectedPilot")
    private String inspectedPilot;

    /**
     * 学员员工编号（飞行）  数据可能为多个，以/分隔
     */
    @TableField("inspectedPilotStaffNo")
    private String inspectedPilotStaffNo;

    /**
     * 检查类别(飞行)
     */
    @TableField("checkType")
    private String checkType;

    /**
     * 资深副驾驶 数据可能为多个，以@分隔
     */
    @TableField("skipper1")
    private String skipper1;

    /**
     * 资深副驾驶员工编号 数据可能为多个，以@分隔
     */
    @TableField("skipper1StaffNo")
    private String skipper1StaffNo;

    /**
     * 资深副驾驶备注
     */
    @TableField("skipper1Remark")
    private String skipper1Remark;

    /**
     * 副驾驶 数据可能为多个，以@分隔
     */
    @TableField("firstVice1")
    private String firstVice1;

    /**
     * 副驾驶员工编号 数据可能为多个，以@分隔
     */
    @TableField("firstVice1StaffNo")
    private String firstVice1StaffNo;

    /**
     * 副驾驶备注
     */
    @TableField("firstVice1Remark")
    private String firstVice1Remark;

    /**
     * 非机组人员 （飞行) 数据可能为多个，以/分隔
     */
    @TableField("accompany1")
    private String accompany1;

    /**
     * 非机组人员ID（飞行) 数据可能为多个，以/分隔
     */
    @TableField("accompany1StaffNo")
    private String accompany1StaffNo;

    /**
     * 执行航班加机组（飞行） 数据可能为多个，以/分隔
     */
    @TableField("pilotDeadheadOps")
    private String pilotDeadheadOps;

    /**
     * 执行航班加机组员工编号（飞行） 数据可能为多个，以/分隔
     */
    @TableField("pilotDeadheadOpsStaffNo")
    private String pilotDeadheadOpsStaffNo;

    /**
     * 非执行航班加机组（飞行） 数据可能为多个，以/分隔
     */
    @TableField("pilotDeadheadOther")
    private String pilotDeadheadOther;

    /**
     * 非执行航班加机组员工编号（飞行） 数据可能为多个，以/分隔
     */
    @TableField("pilotDeadheadOtherStaffNo")
    private String pilotDeadheadOtherStaffNo;

    /**
     * 乘务长 数据可能为多个，以/分隔
     */
    @TableField("chiefStewardCaptain")
    private String chiefStewardCaptain;

    /**
     * 乘务长员工编号 数据可能为多个，以/分隔
     */
    @TableField("chiefStewardCaptainStaffNo")
    private String chiefStewardCaptainStaffNo;

    /**
     * 乘务长备注
     */
    @TableField("chiefStewardCaptainRemark")
    private String chiefStewardCaptainRemark;

    /**
     * 乘务员 数据可能为多个，以/分隔
     */
    @TableField("stewardCap1")
    private String stewardCap1;

    /**
     * 乘务员员工编号 数据可能为多个，以/分隔
     */
    @TableField("stewardCap1StaffNo")
    private String stewardCap1StaffNo;

    /**
     * 乘务员备注
     */
    @TableField("stewardCap1Remark")
    private String stewardCap1Remark;

    /**
     * 安全员 数据可能为多个，以/分隔
     */
    @TableField("safer1")
    private String safer1;

    /**
     * 乘务学员 数据可能为多个，以/分隔
     */
    @TableField("stewards")
    private String stewards;

    /**
     * 乘务学员员工编号 数据可能为多个，以/分隔
     */
    @TableField("stewardsStaffNo")
    private String stewardsStaffNo;

    /**
     * 检查员（乘务）	 数据可能为多个，以/分隔
     */
    @TableField("stewardChecker")
    private String stewardChecker;

    /**
     * 检查员员工编号（乘务） 数据可能为多个，以/分隔
     */
    @TableField("stewardCheckerStaffNo")
    private String stewardCheckerStaffNo;

    /**
     * 教员（乘务） 数据可能为多个，以/分隔
     */
    @TableField("stewardInstructor")
    private String stewardInstructor;

    /**
     * 教员员工编号（乘务） 数据可能为多个，以/分隔
     */
    @TableField("stewardInstructorStaffNo")
    private String stewardInstructorStaffNo;

    /**
     * 检查类别（乘务） 目前无数据
     */
    @TableField("stewardCheckType")
    private String stewardCheckType;

    /**
     * 执行航班加机组（乘务） 数据可能为多个，以/分隔
     */
    @TableField("stewardDeadheadOps")
    private String stewardDeadheadOps;

    /**
     * 执行航班加机组员工编号 （乘务） 数据可能为多个，以/分隔
     */
    @TableField("stewardDeadheadOpsStaffNo")
    private String stewardDeadheadOpsStaffNo;

    /**
     * 非执行航班加机组（乘务） 数据可能为多个，以/分隔
     */
    @TableField("stewardDeadheadOther")
    private String stewardDeadheadOther;

    /**
     * 非执行航班加机组员工编号（乘务）  数据可能为多个，以/分隔
     */
    @TableField("stewardDeadheadOtherStaffNo")
    private String stewardDeadheadOtherStaffNo;

    /**
     * 非机组加机组（乘务）  数据可能为多个，以/分隔
     */
    @TableField("jumpSeating")
    private String jumpSeating;

    /**
     * 非机组加机组员工编号（乘务）数据为0
     */
    @TableField("jumpSeatingStaffNo")
    private String jumpSeatingStaffNo;
    /**
     * 机组名单（飞行） 数据可能为多个，以;分隔
     */
    @TableField("crwPilotInf")
    private String crwPilotInf;

    /**
     * 机组名单（乘务） 数据可能为多个，以;分隔
     */
    @TableField("crwStewardInf")
    private String crwStewardInf;

    /**
     * 航班起飞日期
     */
    @TableField("std")
    private Date std;

    /**
     * 安全员员工编号 数据可能为多个，以;分隔
     */
    @TableField("safer1StaffNo")
    private String safer1StaffNo;

    /**
     * 机长备注
     */
    @TableField("captainRemark")
    private String captainRemark;

    /**
     * 银湖系统的公司编码
     */
    @TableField("companyCode")
    private String companyCode;

    /**
     * 公司名称
     */
    @TableField("companyName")
    private String companyName;

    /**
     * 银湖系统的公司ID
     */
    @TableField("companyId")
    private String companyId;

    /**
     * 签发日期 日期格式
     */
    @TableField("signatureDate")
    private Date signatureDate;

    /**
     * 签发人链接
     */
    @TableField("signatureImageUrl")
    private String signatureImageUrl;

    /**
     * 到达时间
     */
    @TableField("arrTimes")
    private String arrTimes;

    /**
     * 签派员信息 高扬/曹少谦/施健/栾领路,TSN-WUH WUH-SYX  @张若骜, XIY-URC
     */
    @TableField("dispatcher")
    private String dispatcher;

    /**
     * 机务跟机信息 郭茂林,PEK-XMN,过站放行@郭茂林,XMN-PEK,过站放行
     */
    @TableField("engineer")
    private String engineer;

    /**
     * 第一航段计划起飞时间 格式yyyy-MM-dd HH:mm:ss
     */
    @TableField("stdFirst")
    private Date stdFirst;

    /**
     * 巡航机长 数据可能为多个，以/或、分隔
     */
    @TableField("cruisingCaptain")
    private String cruisingCaptain;

    /**
     * 巡航机长员工编号 数据可能为多个，以/分隔。有0值
     */
    @TableField("cruisingCaptains")
    private String cruisingCaptains;

    /**
     * ODS更新时间
     */
    @TableField("updatedTime")
    private Date updatedTime;

    /**
     * 其他加机组
     */
    @TableField("jumpSeating1")
    private String jumpSeating1;

    /**
     * 新机组主键
     */
    @TableField("flightId")
    private Integer flightId;

    /**
     * 航班动态ID
     */
    @TableField("reportId")
    private Integer reportId;

    @ApiModelProperty(value = "创建时间")
    @TableField("gmt_create")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField("gmt_modified")
    private Date gmtModified;

}
