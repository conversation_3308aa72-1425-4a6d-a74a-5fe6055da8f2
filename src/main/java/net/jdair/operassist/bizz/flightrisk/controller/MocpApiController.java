package net.jdair.operassist.bizz.flightrisk.controller;

import com.alibaba.fastjson.JSON;
import com.hnair.opcnet.api.complextype.PageParam;
import com.hnair.opcnet.api.v2.ApiRequest;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.domain.Mocp;
import net.jdair.operassist.common.esbapi.ESBService;
import net.jdair.operassist.common.response.RestResponse;
import net.jdair.operassist.common.response.esb.EsbApiResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;


/**
 * <p>
 * 分页查询MOCP故障通报信息
 * </p>
 */
@Api(value = "MOCP故障通报信息服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/mocp")
public class MocpApiController {

    @Resource
    private ESBService esbService;

    /**
     * 请求 ESB查询mocp数据
     *
     * @return 结果
     */
    @PostMapping("/findMocpViewInfoReportByPage")
    public RestResponse<EsbApiResponse<Mocp>> findMocpViewInfoReportByPage(@RequestBody Map<String, Object> params) {
        ApiRequest apiRequest = new ApiRequest();
        PageParam pageParam = new PageParam();
        pageParam.setPageIndex((Integer) params.get("pageIndex"));
        pageParam.setPageSize(((Integer) params.get("pageSize")));
        apiRequest.setPageParam(pageParam);
        apiRequest.setOption("aviation", params.get("aviation"));
        //航班日期
        apiRequest.setOption("updatedTimeStart", params.get("updatedTimeStart"));
        apiRequest.setOption("updatedTimeEnd", params.get("updatedTimeEnd"));
        EsbApiResponse<Mocp> response;

        try {
            log.info("同步mocp数据查询接口，(第{}页)，开始，参数{}", apiRequest.getPageParam().getPageIndex(), JSON.toJSONString(apiRequest));
            //查询数据
            response = esbService.findMocpViewInfoReportByPage(apiRequest);
            log.info("mocp pageResult response : {}", JSON.toJSONString(response.getPageResult()));
            log.info("同步mocp数据查询接口，(第{}页)，结束", apiRequest.getPageParam().getPageIndex());
            return RestResponse.ok(response);
        } catch (Exception e) {
            log.error("同步mocp数据查询接口，(第{}页)，异常", apiRequest.getPageParam().getPageIndex(), e);
            log.error("同步mocp数据查询接口异常，日志：{}，", e.getMessage());
            e.printStackTrace();
            return RestResponse.exception(e.getMessage());
        }
    }
}
