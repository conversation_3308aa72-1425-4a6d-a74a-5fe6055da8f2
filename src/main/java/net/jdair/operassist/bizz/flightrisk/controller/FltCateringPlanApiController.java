package net.jdair.operassist.bizz.flightrisk.controller;

import com.hnair.opcnet.api.ods.flt.FltCateringPlanRequest;
import com.hnair.opcnet.api.ods.flt.FltCateringPlanResponse;
import com.hnair.opcnet.api.ods.flt.FltPayloadRequest;
import com.hnair.opcnet.api.ods.flt.FltPayloadResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.common.esbapi.ESBService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(value = " com.hnair.opcnet.api.ods.flt.FltCateringPlanApi 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/FltCateringPlanApi")
public class FltCateringPlanApiController {
    @Resource
    private ESBService esbService;


    @ApiOperation(value = "查询获得配餐执行计划数据", notes = "查询获得配餐执行计划 数据")
    @RequestMapping(value = "/getCateringInfo")
    private FltCateringPlanResponse getFltPayload(FltCateringPlanRequest apiRequest){
            FltCateringPlanResponse fltCateringPlanResponse  =
                    esbService.fltCateringPlanApi_getCateringInfo(apiRequest);

        return fltCateringPlanResponse ;

    }

}
