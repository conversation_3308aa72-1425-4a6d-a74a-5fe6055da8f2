package net.jdair.operassist.bizz.flightrisk.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("查询公司航路接口")
@Data
@TableName("flt_operassist_api_companyRoutes")
public class CompanyRoutes {
    @ApiModelProperty("id")
    @TableField("id")
    private Long id;//id
    @ApiModelProperty("源系统主键")
    @TableField("companyEnrouteId")
    private Long companyEnrouteId;//源系统主键
    @ApiModelProperty("航线名称")
    @TableField("codeName")
    private String codeName;//航线名称
    @ApiModelProperty("生效时间")
    @TableField("starttime")
    private String starttime;//生效时间
    @ApiModelProperty("起飞机场")
    @TableField("codeStartad")
    private String codeStartad;//起飞机场
    @ApiModelProperty("落地机场")
    @TableField("codeEndtad")
    private String codeEndtad;//落地机场
    @ApiModelProperty("失效时间")
    @TableField("endtime")
    private String endtime;//失效时间
    @ApiModelProperty("fpl走向")
    @TableField("fpldirections")
    private String fpldirections;//fpl走向
    @ApiModelProperty("CFP走向")
    @TableField("enroutedirction")
    private String enroutedirction;//CFP走向
    @ApiModelProperty("FPL高度层")
    @TableField("fplResult")
    private String fplResult;//FPL高度层
    @ApiModelProperty("CFP高度层")
    @TableField("cfpResult")
    private String cfpResult;//CFP高度层
    @ApiModelProperty("飞越国家")
    @TableField("overcountry")
    private String overcountry;//飞越国家
    @ApiModelProperty("航线批复")
    @TableField("hxpf")
    private String hxpf;//航线批复
    @ApiModelProperty("航线批复结束时间")
    @TableField("hxpfendtime")
    private String hxpfendtime;//航线批复结束时间
    @ApiModelProperty("外籍批复")
    @TableField("wjpf")
    private String wjpf;//外籍批复
    @ApiModelProperty("外籍批复结束时间")
    @TableField("wjpfendtime")
    private String wjpfendtime;//外籍批复结束时间
    @ApiModelProperty("enroutetype")
    @TableField("enroutetype")
    private String enroutetype;//enroutetype
    @ApiModelProperty("inaircraftdata")
    @TableField("inaircraftdata")
    private String inaircraftdata;//inaircraftdata
    @ApiModelProperty("flightAirlineName")
    @TableField("flightAirlineName")
    private String flightAirlineName;//flightAirlineName
    @ApiModelProperty("公司编码")
    @TableField("organizationcode")
    private String organizationcode;//公司编码
    @ApiModelProperty("是否测算")
    @TableField("istest")
    private String istest;//是否测算
    @ApiModelProperty("备注")
    @TableField("txtRmk")
    private String txtRmk;//备注
    @ApiModelProperty("最后修改人")
    @TableField("txtUsername")
    private String txtUsername;//最后修改人
    @ApiModelProperty("最后修改时间")
    @TableField("dateLastmodify")
    private Date dateLastmodify;//最后修改时间
    @ApiModelProperty("zxdescription")
    @TableField("zxdescription")
    private String zxdescription;//zxdescription
    @ApiModelProperty("ODS创建时间")
    @TableField("createdTime")
    private Date createdTime;//ODS创建时间
    @ApiModelProperty("ODS更新时间")
    @TableField("updatedTime")
    private Date updatedTime;//ODS更新时间
    @ApiModelProperty("情报区")
    @TableField("routeFirs")
    private String routeFirs;//情报区
    @ApiModelProperty("批次号")
    @TableField("batchJobNo")
    private String batchJobNo;//批次号
    @ApiModelProperty("ODS删除标记")
    @TableField("deleted")
    private boolean deleted;//ODS删除标记
    @ApiModelProperty("数据来源数据库")
    @TableField("dbUser")
    private String dbUser;//数据来源数据库
    @ApiModelProperty("保存计算总距离")
    @TableField("routeDistance")
    private String routeDistance;//保存计算总距离


}
