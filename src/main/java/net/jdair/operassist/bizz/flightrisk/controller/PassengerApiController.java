package net.jdair.operassist.bizz.flightrisk.controller;

import com.hnair.opcnet.api.ods.wi.GetWeatherMessageRequest;
import com.hnair.opcnet.api.ods.wi.GetWeatherMessageResponse;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.common.esbapi.ESBService;
import net.jdair.operassist.common.response.RestResponse;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@Api(value = "WeatherMessages 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/PassengerApi")
public class PassengerApiController {
    @Resource
    private ESBService esbService;
    @ApiOperation(
            value = "esb任务书接口接口转发 查询航班旅客汇总信息（T+1） com.hnair.opcnet.api.ods.psr.PassengerApi.findPsrStat ",
            notes = "esb任务书接口接口转发 ")
    @RequestMapping(value = "findPsrStat" )
    public ApiResponse findPsrStat(@RequestParam Map<String, Object> options , ApiRequest apiRequest) {
        Integer pageIndex =0 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }
        log.info("同步查询航班旅客汇总信息（T+1） com.hnair.opcnet.api.ods.psr.PassengerApi.findPsrStat(第{}页)，开始", pageIndex);
        //查询数据
        ApiResponse response = esbService.findPsrStat(apiRequest);
        log.info("同步查询航班旅客汇总信息（T+1） com.hnair.opcnet.api.ods.psr.PassengerApi.findPsrStat(第{}页)，结束", pageIndex);

        return response;
    }


}
