package net.jdair.operassist.bizz.flightrisk.controller;

import com.alibaba.fastjson.JSONArray;
import com.hnair.opcnet.api.v2.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.common.esbapi.ESBService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;


@Api(value = " HucDutyApi 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/HucDutyApi")
public class HucDutyApiController {
    @Resource
    private ESBService esbService;


   @ApiOperation(value = "查询 hucDutyApi_getDutyInfoByPage 数据", notes = "查询 hucDutyApi_getDutyInfoByPage 数据")
   @RequestMapping(value = "/getDutyInfoByPage")
   public ApiResponse getDutyInfoByPage(@RequestParam Map<String, Object> options ,
                                        ApiRequest apiRequest) {
       try{
           apiRequest.getOptions().putAll(options);
       }catch(Exception e){
           log.error("获取分页参数出错",e);
       }

       log.info("ESB查询 hucDutyApi_getDutyInfoByPage 接口，请求：{}", JSONArray.toJSONString(apiRequest));
       long start = System.currentTimeMillis();
       ApiResponse apiResponse = esbService.hucDutyApi_getDutyInfoByPage(apiRequest);
       long end = System.currentTimeMillis();
       log.info("ESB查询 hucDutyApi_getDutyInfoByPage 接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
       return apiResponse;
   }


    @ApiOperation(value = "查询 hucDutyApi_getMHucRoleInfoByPage 数据", notes = "查询 hucDutyApi_getMHucRoleInfoByPage 数据")
    @RequestMapping(value = "/getMHucRoleInfoByPage")
    public ApiResponse getMHucRoleInfoByPage(@RequestParam Map<String, Object> options ,
                                             ApiRequest     apiRequest                ) {

                try{
                    apiRequest.getOptions().putAll(options);
                }catch(Exception e){
                    log.error("获取分页参数出错",e);
                }

                log.info("ESB查询esbService.hucDutyApi_getMHucRoleInfoByPage接口，请求：{}", JSONArray.toJSONString(apiRequest));
                long start = System.currentTimeMillis();
                ApiResponse apiResponse = esbService.hucDutyApi_getMHucRoleInfoByPage(apiRequest);
                long end = System.currentTimeMillis();
                log.info("ESB查询esbService.hucDutyApi_getMHucRoleInfoByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
                return apiResponse;

    }






}
