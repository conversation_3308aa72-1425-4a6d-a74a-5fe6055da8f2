package net.jdair.operassist.bizz.flightrisk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.jdair.operassist.bizz.flightrisk.domain.FltFocStandardInfo;
import net.jdair.operassist.bizz.flightrisk.domain.OdsRoster;
import net.jdair.operassist.bizz.flightrisk.vo.OdsRosterSearchVo;

import java.util.List;

/**
 * 排班任务书的Service
 *
 * <AUTHOR>
 */
public interface OdsRosterService {

    /**
     * 批量插入数据
     *
     * @param batchList 批量数据
     */
    void batchInsert(List<OdsRoster> batchList);

    /**
     * 分页查询
     *
     * @param param 参数
     * @return 结果
     */
    Page<OdsRoster> listByPage(OdsRosterSearchVo param);

    /**
     * 查询
     * @return 结果
     * @param param 参数
     */
    OdsRoster queryCaptain(FltFocStandardInfo param);
}
