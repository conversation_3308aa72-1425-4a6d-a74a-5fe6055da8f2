package net.jdair.operassist.bizz.flightrisk.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnair.opcnet.api.complextype.PageParam;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.domain.DutyRosterV2;
import net.jdair.operassist.bizz.flightrisk.domain.FltFocStandardInfo;
import net.jdair.operassist.bizz.flightrisk.domain.OdsRoster;
import net.jdair.operassist.bizz.flightrisk.mapper.OdsRosterMapper;
import net.jdair.operassist.bizz.flightrisk.service.OdsRosterService;
import net.jdair.operassist.bizz.flightrisk.vo.OdsRosterSearchVo;
import net.jdair.operassist.common.util.LocalDateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OdsRosterServiceImpl extends ServiceImpl<OdsRosterMapper, OdsRoster> implements OdsRosterService {

    private final OdsRosterMapper odsRosterMapper;

    @Autowired
    public OdsRosterServiceImpl(OdsRosterMapper odsRosterMapper) {
        this.odsRosterMapper = odsRosterMapper;
    }

    @Override
    public Page<OdsRoster> listByPage(OdsRosterSearchVo param) {
        log.debug("正在执行分页查询OdsRoster: page = {} pageSize = {} factor = {}", param.getPage(), param.getPageSize(), JSON.toJSONString(param));
        LambdaQueryWrapper<OdsRoster> queryWrapper = new LambdaQueryWrapper<>();
        //fltDate参数
        if (param.getFltDate() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formatDate = sdf.format(param.getFltDate());
            queryWrapper.apply("date_format(fltDate,'%Y-%m-%d') = {0}", formatDate);
        }
        //fltIds参数
        if (StringUtils.isNotBlank(param.getFltIds())) {
            queryWrapper.eq(OdsRoster::getFltIds, param.getFltIds());
        }
        //acNo
        if (StringUtils.isNotBlank(param.getAcNo())) {
            queryWrapper.eq(OdsRoster::getAcNo, param.getAcNo());
        }
        //routes
        if (StringUtils.isNotBlank(param.getRoutes())) {
            queryWrapper.eq(OdsRoster::getRoutes, param.getRoutes());
        }
        //accd
        if (StringUtils.isNotBlank(param.getAccd())) {
            queryWrapper.eq(OdsRoster::getAccd, param.getAccd());
        }
        //depStn
        if (StringUtils.isNotBlank(param.getDepStn())) {
            queryWrapper.apply("left(routes,3) = {0}", param.getDepStn());
        }
        //arrStn
        if (StringUtils.isNotBlank(param.getArrStn())) {
            queryWrapper.apply("right(routes,3) = {0}", param.getArrStn());
        }
        queryWrapper.orderByDesc(OdsRoster::getId);
        //查询数据
        Page<OdsRoster> result = super.page(new Page<>(param.getPage(), param.getPageSize()), queryWrapper);
        log.debug("分页查询OdsRoster完毕: 结果数 = {} ", result.getRecords().size());
        return result;
    }

    @Override
    public OdsRoster queryCaptain(FltFocStandardInfo param) {
//        B. 航班日期（当地时间）(fltDate)=当日日期，A. 航班号（flightNo）=B.航班号（fltIds），
//     * A.飞机号（acLongNo）=B.飞机号（acNo），A.航线（airlineCode）=B. 航线（routes）。
        LambdaQueryWrapper<OdsRoster> queryWrapper = new LambdaQueryWrapper<>();
        LocalDate now = LocalDate.now();
        String fltDate = now.format(DateTimeFormatter.ofPattern(LocalDateUtils.YYYY_MM_DD));
        queryWrapper.apply("date_format(fltDate,'%Y-%m-%d') = {0}", fltDate);
        queryWrapper.eq(OdsRoster::getFltIds, param.getFlightNo());
        queryWrapper.eq(OdsRoster::getAcNo, param.getAcLongNo());
        String routes = param.getDepStn() + "-" + param.getArrStn();
        queryWrapper.eq(OdsRoster::getRoutes, routes);
        List<OdsRoster> odsRosters = odsRosterMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(odsRosters)){
            return odsRosters.get(0);
        }else {
            return null;
        }
    }

    @Override
    public void batchInsert(List<OdsRoster> batchList) {
        log.debug("开始批量插入OdsRoster");
        odsRosterMapper.batchInsert(batchList);
        log.debug("批量插入OdsRoster成功");
    }



}
