package net.jdair.operassist.bizz.flightrisk.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnair.opcnet.api.complextype.PageResult;
import com.hnair.opcnet.api.complextype.Result;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.domain.DesiFpar;
import net.jdair.operassist.bizz.flightrisk.domain.MNfmsFmalFcti;
import net.jdair.operassist.bizz.flightrisk.service.MNfmsFmalFctiService;
import net.jdair.operassist.common.esbapi.ESBService;
import net.jdair.operassist.common.response.RestResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Api(value = " MNfnsApi 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/mNfnsApi")
public class MNfnsApiController {
    @Resource
    private ESBService esbService;

    @Resource
    private MNfmsFmalFctiService mNfmsFmalFctiService;

    @ApiOperation(value = "查询 mNfnsApi_findMNfmsFmalFctiByPage 数据", notes = "查询 mNfnsApi_findMNfmsFmalFctiByPage 数据")
    @RequestMapping(value = "/findMNfmsFmalFctiByPage")
    private ApiResponse findMNfmsFmalFctiByPage(@RequestParam Map<String, Object> options , ApiRequest apiRequest){
            Integer pageIndex =0 ;
            try{
                if(apiRequest.getPageParam()!=null){
                    pageIndex = apiRequest.getPageParam().getPageIndex();
                };
                apiRequest.getOptions().putAll(options);
            }catch(Exception e){
                log.error("获取分页参数出错",e);
            }

            ApiResponse response =  esbService.mNfnsApi_findMNfmsFmalFctiByPage(apiRequest);
        return response ;
    }



    @ApiOperation(value = "查询 csdApi_findMNfmsFmalFctiByPage 数据从中台缓存获得", notes = "查询 csdApi_findMNfmsFmalFctiByPage 数据")
    @RequestMapping(value = "/findMNfmsFmalFctiByPage_cache")
    private ApiResponse findMNfmsFmalFctiByPage_cache(@RequestParam Map<String, Object> options , ApiRequest apiRequest){
        Page<DesiFpar> page = new Page<>();
        Integer pageIndex =1 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
                if(pageIndex!=null){
                    page.setCurrent(pageIndex);
                }
                Integer pageSize = apiRequest.getPageParam().getPageSize();
                if(pageSize!=null){
                    page.setSize(pageSize);
                }else{
                    page.setSize(20);
                }
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }

        Page<MNfmsFmalFcti> desiFparPage = mNfmsFmalFctiService.findMNfmsFmalFctiByPage_cache(options,page);


        ApiResponse response = new ApiResponse();

        for(MNfmsFmalFcti mNfmsFmalFcti :  desiFparPage.getRecords()){
            Map<String,Object>  map =  BeanUtil.beanToMap(mNfmsFmalFcti);
            response.getData().add(map);
        }
        PageResult pageResult = new PageResult();
        pageResult.setTotalRecords((int)desiFparPage.getTotal());
        pageResult.setPageIndex((int)desiFparPage.getCurrent());
        pageResult.setPageSize((int)desiFparPage.getSize());
        pageResult.setTotalPages((int)desiFparPage.getPages());

        pageResult.setPageRecords(desiFparPage.getRecords().size());

        response.setPageResult(pageResult);
        Result result = new Result();
        result.setResultCode(1);
        result.setResultMsg("调用成功");
        response.setResult(result);
        return response ;
    }





    @ApiOperation("定时更新数据")
    @PostMapping(value = "/loadEsbDataToLocalDb_MNfmsFmalFcti")
    public RestResponse<String> loadEsbDataToLocalDb_MNfmsFmalFcti(@RequestParam(name = "days") String days)throws Exception {
        mNfmsFmalFctiService.loadEsbDataToLocalDb(days);
        //返回结果
        return RestResponse.ok("success");

    }

}
