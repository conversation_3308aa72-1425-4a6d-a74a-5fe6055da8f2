package net.jdair.operassist.bizz.flightrisk.controller;


import com.alibaba.fastjson.JSONArray;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.common.esbapi.ESBService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Api(value = "napsApi_findCompanyRoutesByPage 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/companyRoutes")
public class CompanyRoutesController {
    @Resource
    private ESBService esbService;

    @ApiOperation(value = "查询 napsApi_findCompanyRoutesByPage 数据",
                  notes = "查询 napsApi_findCompanyRoutesByPage 数据")
    @RequestMapping(value = "/findCompanyRoutesByPage")
    private ApiResponse findCompanyRoutesByPage(@RequestParam Map<String, Object> options ,
                                                ApiRequest apiRequest) {
        Integer pageIndex =0 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }
        // log.info("同步航班动态数据查询接口(第{}页)，开始", pageIndex);

        log.info("ESB查询esbService.napsApi_findCompanyRoutesByPage  接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = esbService.napsApi_findCompanyRoutesByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询esbService.napsApi_findCompanyRoutesByPage 接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }

}
