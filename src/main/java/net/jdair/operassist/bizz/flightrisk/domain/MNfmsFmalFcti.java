package net.jdair.operassist.bizz.flightrisk.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("分页获取航班协同时隙信息(FCTI)")
@TableName("flt_operassist_api_mNfmsFmalFcti")
@Data
public class MNfmsFmalFcti {

    @ApiModelProperty("ID")
    @TableField("id")
    @TableId
    private Long id;//ID
    @ApiModelProperty("ESB_NFMS_FMAL_MSG_XML ID")
    @TableField("msgXmlId")
    private String msgXmlId;//ESB_NFMS_FMAL_MSG_XML ID
    @ApiModelProperty("全球航班唯一标识符")
    @TableField("gufi")
    private String gufi;//全球航班唯一标识符
    @ApiModelProperty("航班唯一标识符")
    @TableField("guid")
    private String guid;//航班唯一标识符
    @ApiModelProperty("航空器识别标识")
    @TableField("arcid")
    private String arcid;//航空器识别标识
    @ApiModelProperty("计划起飞机场")
    @TableField("sdepap")
    private String sdepap;//计划起飞机场
    @ApiModelProperty("计划目的地机场")
    @TableField("sarrap")
    private String sarrap;//计划目的地机场
    @ApiModelProperty("计划离港时间")
    @TableField("sobt")
    private String sobt;//计划离港时间
    @ApiModelProperty("预计撤轮挡时间")
    @TableField("eobt")
    private String eobt;//预计撤轮挡时间
    @ApiModelProperty("预计降落时间（O）")
    @TableField("eldt")
    private String eldt;//预计降落时间（O）
    @ApiModelProperty("实际目的地机场")
    @TableField("rarrap")
    private String rarrap;//实际目的地机场
    @ApiModelProperty("状态")
    @TableField("locktag")
    private String locktag;//状态
    @ApiModelProperty("计算起飞时间")
    @TableField("ctot")
    private String ctot;//计算起飞时间
    @ApiModelProperty("时间标识")
    @TableField("ctotInfo")
    private String ctotInfo;//时间标识
    @ApiModelProperty("计算撤轮挡时间")
    @TableField("cobt")
    private String cobt;//计算撤轮挡时间
    @ApiModelProperty("航班受控标识")
    @TableField("resflag")
    private String resflag;//航班受控标识
    @ApiModelProperty("建议飞行高度")
    @TableField("sfl")
    private String sfl;//建议飞行高度
    @ApiModelProperty("流控标识")
    @TableField("ftmid")
    private String ftmid;//流控标识
    @ApiModelProperty("流控主要内容")
    @TableField("ftmicontent")
    private String ftmicontent;//流控主要内容
    @ApiModelProperty("前序航班航班号")
    @TableField("farcid")
    private String farcid;//前序航班航班号
    @ApiModelProperty("前序航班计算起飞时间")
    @TableField("fctot")
    private String fctot;//前序航班计算起飞时间
    @ApiModelProperty("前序航班状态")
    @TableField("fstat")
    private String fstat;//前序航班状态
    @ApiModelProperty("系统推测初始目标撤轮档时间")
    @TableField("otobt")
    private String otobt;//系统推测初始目标撤轮档时间
    @ApiModelProperty("目标撤轮档时间（系统目前采纳的时间）")
    @TableField("tobt")
    private String tobt;//目标撤轮档时间（系统目前采纳的时间）
    @ApiModelProperty("入池标识")
    @TableField("wpflag")
    private String wpflag;//入池标识
    @ApiModelProperty("入池说明")
    @TableField("wpexplain")
    private String wpexplain;//入池说明
    @ApiModelProperty("创建时间")
    @TableField("srcCreatedTime")
    private Date srcCreatedTime;//创建时间
    @ApiModelProperty("修改时间")
    @TableField("srcUpdatedTime")
    private Date srcUpdatedTime;//修改时间
    @ApiModelProperty("发送时间")
    @TableField("sendTime")
    private Date sendTime;//发送时间
    @ApiModelProperty("唯一标识该消息")
    @TableField("messageSequence")
    private String messageSequence;//唯一标识该消息
    @ApiModelProperty("航班号")
    @TableField("flightNo")
    private String flightNo;//航班号
    @ApiModelProperty("航班日期-北京")
    @TableField("datopChn")
    private String datopChn;//航班日期-北京
    @ApiModelProperty("计划起飞机场(三字码)")
    @TableField("depapT")
    private String depapT;//计划起飞机场(三字码)
    @ApiModelProperty("计划目的地机场(三字码)")
    @TableField("arrapT")
    private String arrapT;//计划目的地机场(三字码)
    @ApiModelProperty("NFMS_FMAL_FCTI表ID主键")
    @TableField("srcId")
    private String srcId;//NFMS_FMAL_FCTI表ID主键
    @ApiModelProperty("是否已删除")
    @TableField("deleted")
    private String deleted;//是否已删除
    @ApiModelProperty("创建时间")
    @TableField("createdTime")
    private String createdTime;//创建时间
    @ApiModelProperty("更新时间")
    @TableField("updatedTime")
    private Date updatedTime;//更新时间
    @ApiModelProperty("JOB批次号，ETL处理")
    @TableField("batchJobNo")
    private String batchJobNo;//JOB批次号，ETL处理
    @ApiModelProperty("最早原计划离岗时间")
    @TableField("srcStd")
    private String srcStd;//最早原计划离岗时间
    @ApiModelProperty("发送时间")
    @TableField("arSendTime")
    private Date arSendTime;//发送时间
    @ApiModelProperty("ODS更新时间")
    @TableField("maxUpdatedTime")
    private Date maxUpdatedTime;//ODS更新时间
    @ApiModelProperty("实际降落时间")
    @TableField("aldt")
    private String aldt;//实际降落时间
    @ApiModelProperty("实际起飞时间")
    @TableField("atot")
    private String atot;//实际起飞时间
    @ApiModelProperty("预测降落跑道")
    @TableField("arwy")
    private String arwy;//预测降落跑道
    @ApiModelProperty("预测起飞跑道")
    @TableField("drwy")
    private String drwy;//预测起飞跑道
    @ApiModelProperty("最新的预计落地时间")
    @TableField("pldt")
    private String pldt;//最新的预计落地时间
    @ApiModelProperty("最新的预计起飞时间")
    @TableField("ptot")
    private String ptot;//最新的预计起飞时间


}