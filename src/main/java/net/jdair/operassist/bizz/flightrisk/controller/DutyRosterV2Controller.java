//package net.jdair.operassist.bizz.flightrisk.controller;
//
//
//import com.alibaba.fastjson.JSONArray;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.hnair.opcnet.api.complextype.PageParam;
//import com.hnair.opcnet.api.ods.ntm.NtmApprovedNotam;
//import com.hnair.opcnet.api.ods.ntm.NtmApprovedNotamRequest;
//import com.hnair.opcnet.api.ods.ntm.NtmApprovedNotamResponse;
//import com.hnair.opcnet.api.v2.ApiRequest;
//import com.hnair.opcnet.api.v2.ApiResponse;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import net.jdair.operassist.bizz.flightrisk.vo.NtmApprovedNotamSearchVo;
//import net.jdair.operassist.common.esbapi.ESBService;
//import net.jdair.operassist.common.page.TableDataInfo;
//import net.jdair.operassist.common.response.RestResponse;
//import org.springframework.beans.BeanUtils;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import java.util.Map;
//
//@Deprecated
//@Api(value = "NtmApprovedNotam 服务接口")
//@Slf4j
//@RestController
//@RequestMapping("/operassist/odsRoster")
//public class DutyRosterV2Controller {
//    @Resource
//    private ESBService esbService;
//
//
//    @ApiOperation(
//            value = "esb机组排班接口接口转发com.hnair.opcnet.api.ods.roster.OdsRosterApi.getDutyRosterV2",
//            notes = "esb机组排班接口接口转发 ")
//    @RequestMapping(value = "findFlightReportV2" )
//    public ApiResponse getDutyRosterV2(@RequestParam Map<String, Object> options , ApiRequest apiRequest) {
//        Integer pageIndex =1 ;
//        try{
//            if(apiRequest.getPageParam()!=null){
//                pageIndex = apiRequest.getPageParam().getPageIndex();
//            };
//            apiRequest.getOptions().putAll(options);
//        }catch(Exception e){
//            log.error("获取分页参数出错",e);
//        }
//        log.info("同步机组排班数据查询接口(第{}页)，开始", pageIndex);
//        //查询数据
//        ApiResponse response = esbService.odsRosterApi_getDutyRosterV2(apiRequest);
//        log.info("同步机组排班数据查询接口(第{}页)，结束", pageIndex);
//
//        return response;
//    }
//}
