package net.jdair.operassist.bizz.flightrisk.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnair.opcnet.api.complextype.PageParam;
import com.hnair.opcnet.api.v2.ApiRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.vo.AircraftGaultInfoSearchVo;
import net.jdair.operassist.common.esbapi.ESBService;
import net.jdair.operassist.common.page.TableDataInfo;
import net.jdair.operassist.common.response.RestResponse;
import net.jdair.operassist.common.response.esb.AircraftGaultInfo;
import net.jdair.operassist.common.response.esb.EsbApiResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Handler;


/**
 * <p>
 * 飞机故障描述
 * </p>
 *
 */
@Api(value = "AircraftGaultInfo 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/AircraftGaultInfo")
public class AircraftGaultInfoController {

    @Resource
    private ESBService esbService;

    /**
     * 查询分页数据
     */
    @ApiOperation(value = "查询 AircraftGaultInfo 分页数据", notes = "查询 AircraftGaultInfo 分页数据")
    @PostMapping(value = "/list")
    public RestResponse<Page<AircraftGaultInfo>> listByPage(@RequestBody AircraftGaultInfoSearchVo param) {
        ApiRequest apiRequest = new ApiRequest();
        PageParam pageParam = new PageParam();
        pageParam.setPageIndex(param.getPage());
        pageParam.setPageSize(param.getPageSize());
        apiRequest.setPageParam(pageParam);


        // 查询条件设置
        Field[] fields =  param.getClass().getDeclaredFields();

        try {
            for (Field field : fields) {
                field.setAccessible(true);
                String key = field.getName();// 获取属性名
                if ("page".equals(key) || "pageSize".equals(key)) {
                    continue;
                }
                String method = key.substring(0, 1).toUpperCase() + key.substring(1);// 将属性首字符大写，方便get & set 方法
                Method getmethod = param.getClass().getMethod("get" + method);// 获取 get 方法
                String value = (String) getmethod.invoke(param);// 通过 get 获取值
                if (value != null) {
                    apiRequest.getOptions().put(key, value);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        EsbApiResponse<AircraftGaultInfo> esbApiResponse = esbService.getAircraftGaultInfoByPage(apiRequest);

        return RestResponse.ok(new TableDataInfo(esbApiResponse.getData(), esbApiResponse.getPageResult().getTotalRecords()));
    }

}
