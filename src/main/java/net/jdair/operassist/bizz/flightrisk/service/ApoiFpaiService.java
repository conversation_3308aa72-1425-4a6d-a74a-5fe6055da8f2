package net.jdair.operassist.bizz.flightrisk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.jdair.operassist.bizz.flightrisk.domain.ApoiFpai;
import net.jdair.operassist.bizz.flightrisk.domain.ApoiFpdi;

import java.util.Date;
import java.util.Map;

public interface ApoiFpaiService {

    public Date maxUpdateTime();

    public void loadEsbDataToLocalDb(String days)throws Exception;

    Page<ApoiFpai> findApoiFpaiByPage_cache(Map<String, Object> options, Page page);
}
