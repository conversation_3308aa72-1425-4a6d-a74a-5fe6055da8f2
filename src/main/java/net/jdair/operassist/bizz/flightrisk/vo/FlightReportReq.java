package net.jdair.operassist.bizz.flightrisk.vo;

import com.hnair.opcnet.api.complextype.PageParam;
//import com.hnatourism.b2b.common.form.BaseForm;

import java.util.List;

public class FlightReportReq extends BaseForm{
	private static final long serialVersionUID = 1L;
	/** * 航班号 */
	private String flightNos;
	/** * 航班日期 */
	private String flightDate;
	/** * 飞机号*/
	private String acNo;
	/** * 航线号*/
	private String routes;
	/** * 机型 */
	private String aircraftType;
	/** * 删除标识*/
	private List<String> deleteds;
	/** * 出发地机场三字码 */
	private String departStation;
	/** * 目的地机场三字码*/
	private String arrivalStation;
	/** * std第一段时间开始*/
	private String stdFirstTimeStart;
	/** * std第一段时间结束*/
	private String stdFirstTimeEnd;
	/** * 是否查询签派员、机务跟机数据(默认true)*/
	private boolean EngineersAndDispatchers;
	/** * 分页参数*/
	private PageParam pageParam;
	/** * 更新时间开始*/
	private String updatedTimeStart;
	/** * 更新时间结束*/
	private String updatedTimeEnd;
	public String getFlightNos() {
		return flightNos;
	}
	public void setFlightNos(String flightNos) {
		this.flightNos = flightNos;
	}
	public String getFlightDate() {
		return flightDate;
	}
	public void setFlightDate(String flightDate) {
		this.flightDate = flightDate;
	}
	public String getAcNo() {
		return acNo;
	}
	public void setAcNo(String acNo) {
		this.acNo = acNo;
	}
	public String getRoutes() {
		return routes;
	}
	public void setRoutes(String routes) {
		this.routes = routes;
	}
	public String getAircraftType() {
		return aircraftType;
	}
	public void setAircraftType(String aircraftType) {
		this.aircraftType = aircraftType;
	}
	public String getDepartStation() {
		return departStation;
	}
	public void setDepartStation(String departStation) {
		this.departStation = departStation;
	}
	public String getArrivalStation() {
		return arrivalStation;
	}
	public void setArrivalStation(String arrivalStation) {
		this.arrivalStation = arrivalStation;
	}
	public String getStdFirstTimeStart() {
		return stdFirstTimeStart;
	}
	public void setStdFirstTimeStart(String stdFirstTimeStart) {
		this.stdFirstTimeStart = stdFirstTimeStart;
	}
	public String getStdFirstTimeEnd() {
		return stdFirstTimeEnd;
	}
	public void setStdFirstTimeEnd(String stdFirstTimeEnd) {
		this.stdFirstTimeEnd = stdFirstTimeEnd;
	}
	public List<String> getDeleteds() {
		return deleteds;
	}
	public void setDeleteds(List<String> deleteds) {
		this.deleteds = deleteds;
	}
	public boolean isEngineersAndDispatchers() {
		return EngineersAndDispatchers;
	}
	public void setEngineersAndDispatchers(boolean engineersAndDispatchers) {
		EngineersAndDispatchers = engineersAndDispatchers;
	}
	public PageParam getPageParam() {
		return pageParam;
	}
	public void setPageParam(PageParam pageParam) {
		this.pageParam = pageParam;
	}
	public String getUpdatedTimeStart() {
		return updatedTimeStart;
	}
	public void setUpdatedTimeStart(String updatedTimeStart) {
		this.updatedTimeStart = updatedTimeStart;
	}
	public String getUpdatedTimeEnd() {
		return updatedTimeEnd;
	}
	public void setUpdatedTimeEnd(String updatedTimeEnd) {
		this.updatedTimeEnd = updatedTimeEnd;
	}

}
