package net.jdair.operassist.bizz.flightrisk.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("航班计划调整信息")
@TableName("flt_operassist_api_apoiFpdi")
@Data
public class ApoiFpdi {


    @ApiModelProperty("主键")
    @TableField("id")
    @TableId
    private Long id;//主键
    @ApiModelProperty("源表主键")
    @TableField("csdId")
    private Long csdId;//源表主键
    @ApiModelProperty("CSD_MSG_XML表ID主键")
    @TableField("srcId")
    private Long srcId;//CSD_MSG_XML表ID主键

    @ApiModelProperty("航空器识别标志")
    @TableField("callsign")
    private String callsign;//航空器识别标志
    @ApiModelProperty("航班号")
    @TableField("flightNo")
    private String flightNo;//航班号
    @ApiModelProperty("航班日期-北京")
    @TableField("datopChn")
    private String datopChn;//航班日期-北京
    @ApiModelProperty("航班唯一标识符")
    @TableField("gufi")
    private String gufi;//航班唯一标识符
    @ApiModelProperty("航空器注册号")
    @TableField("regnumber")
    private String regnumber;//航空器注册号
    @ApiModelProperty("航空器地址码")
    @TableField("addresscode")
    private String addresscode;//航空器地址码
    @ApiModelProperty("计划离港时间")
    @TableField("sobt")
    private String sobt;//计划离港时间
    @ApiModelProperty("计划到港时间")
    @TableField("sibt")
    private String sibt;//计划到港时间
    @ApiModelProperty("计划起飞机场")
    @TableField("depap")
    private String depap;//计划起飞机场
    @ApiModelProperty("计划目的地机场")
    @TableField("arrap")
    private String arrap;//计划目的地机场
    @ApiModelProperty("计划起飞机场(三字码)")
    @TableField("depapt")
    private String depapt;//计划起飞机场(三字码)
    @ApiModelProperty("计划目的地机场(三字码)")
    @TableField("arrapt")
    private String arrapt;//计划目的地机场(三字码)
    @ApiModelProperty("预计撤轮档时间")
    @TableField("eobt")
    private String eobt;//预计撤轮档时间
    @ApiModelProperty("目标撤轮挡时间")
    @TableField("tobt")
    private String tobt;//目标撤轮挡时间
    @ApiModelProperty("开始保洁时间")
    @TableField("startCleantime")
    private Date startCleantime;//开始保洁时间
    @ApiModelProperty("完成保洁时间")
    @TableField("endCleantime")
    private Date endCleantime;//完成保洁时间
    @ApiModelProperty("开始配餐时间")
    @TableField("startCarteringTime")
    private Date startCarteringTime;//开始配餐时间
    @ApiModelProperty("完成配餐时间")
    @TableField("endCarteringTime")
    private Date endCarteringTime;//完成配餐时间
    @ApiModelProperty("开始供油时间")
    @TableField("startFuelTime")
    private Date startFuelTime;//开始供油时间
    @ApiModelProperty("完成供油时间")
    @TableField("endFuelTime")
    private Date endFuelTime;//完成供油时间
    @ApiModelProperty("开始排污时间")
    @TableField("startSewageTime")
    private Date startSewageTime;//开始排污时间
    @ApiModelProperty("完成排污时间")
    @TableField("endSewageTime")
    private Date endSewageTime;//完成排污时间
    @ApiModelProperty("开始加清水时间")
    @TableField("startWaterTime")
    private Date startWaterTime;//开始加清水时间
    @ApiModelProperty("完成加清水时间")
    @TableField("endWaterTime")
    private Date endWaterTime;//完成加清水时间
    @ApiModelProperty("开始除冰时间")
    @TableField("aczt")
    private String aczt;//开始除冰时间
    @ApiModelProperty("完成除冰时间")
    @TableField("aezt")
    private String aezt;//完成除冰时间
    @ApiModelProperty("开始行李装载时间")
    @TableField("startLuggageLoadingTime")
    private Date startLuggageLoadingTime;//开始行李装载时间
    @ApiModelProperty("完成行李装载时间")
    @TableField("endLuggageLoadingTime")
    private Date endLuggageLoadingTime;//完成行李装载时间
    @ApiModelProperty("开始登机时间")
    @TableField("asbt")
    private String asbt;//开始登机时间
    @ApiModelProperty("完成登机时间")
    @TableField("aebt")
    private String aebt;//完成登机时间
    @ApiModelProperty("离港客梯车撤离时间")
    @TableField("lsw")
    private String lsw;//离港客梯车撤离时间
    @ApiModelProperty("离港摆渡车撤离时间")
    @TableField("depshuttleoffTime")
    private Date depshuttleoffTime;//离港摆渡车撤离时间
    @ApiModelProperty("拖车到位时间")
    @TableField("trailerinplaceTime")
    private Date trailerinplaceTime;//拖车到位时间
    @ApiModelProperty("离桥时间")
    @TableField("aadt")
    private String aadt;//离桥时间
    @ApiModelProperty("机务维修人员到位时间")
    @TableField("maintenancemanreadyTime")
    private Date maintenancemanreadyTime;//机务维修人员到位时间
    @ApiModelProperty("机务放行时间")
    @TableField("crewclearanceTime")
    private Date crewclearanceTime;//机务放行时间
    @ApiModelProperty("值机关闭时间")
    @TableField("checkincloseTIme")
    private Date checkincloseTIme;//值机关闭时间
    @ApiModelProperty("实际离港时间")
    @TableField("aobt")
    private String aobt;//实际离港时间
    @ApiModelProperty("离港航班地面移交时间")
    @TableField("depgroundhandoverTime")
    private Date depgroundhandoverTime;//离港航班地面移交时间
    @ApiModelProperty("离港航班停机位")
    @TableField("depstand")
    private String depstand;//离港航班停机位
    @ApiModelProperty("离港航班登机口")
    @TableField("depgate")
    private String depgate;//离港航班登机口
    @ApiModelProperty("值机人数")
    @TableField("checkinpassengersum")
    private Long checkinpassengersum;//值机人数
    @ApiModelProperty("已过安检旅客人数")
    @TableField("securitycheckedpassengersum")
    private Long securitycheckedpassengersum;//已过安检旅客人数
    @ApiModelProperty("登机人数")
    @TableField("boardingpassengersum")
    private Long boardingpassengersum;//登机人数
    @ApiModelProperty("来源系统")
    @TableField("sourcesystemId")
    private String sourcesystemId;//来源系统
    @ApiModelProperty("发送时间")
    @TableField("shendTime")
    private Date shendTime;//发送时间
    @ApiModelProperty("源创建时间")
    @TableField("srcCreatedTime")
    private Date srcCreatedTime;//源创建时间
    @ApiModelProperty("源更新时间")
    @TableField("srcUpdatedTime")
    private Date srcUpdatedTime;//源更新时间
    @ApiModelProperty("删除状态")
    @TableField("deleted")
    private Integer deleted;//删除状态
    @ApiModelProperty("ODS创建时间")
    @TableField("createdTime")
    private Date createdTime;//ODS创建时间
    @ApiModelProperty("ODS更新时间")
    @TableField("updatedTime")
    private Date updatedTime;//ODS更新时间
    @ApiModelProperty("值机开始时间")
    @TableField("checkinOpenTime")
    private Date checkinOpenTime;//值机开始时间
    @ApiModelProperty("除冰位置")
    @TableField("deicl")
    private String deicl;//除冰位置
    @ApiModelProperty("除冰方式")
    @TableField("deicm")
    private String deicm;//除冰方式
    @ApiModelProperty("除冰坪号码")
    @TableField("deicn")
    private String deicn;//除冰坪号码
    @ApiModelProperty("最早原计划离岗时间")
    @TableField("srcStd")
    private String srcStd;//最早原计划离岗时间
    @ApiModelProperty("发送时间")
    @TableField("arSendTime")
    private Date arSendTime;//发送时间
    @ApiModelProperty("ODS更新时间")
    @TableField("maxUpdatedTime")
    private Date maxUpdatedTime;//ODS更新时间

}
