package net.jdair.operassist.bizz.flightrisk.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import net.jdair.operassist.bizz.flightrisk.domain.FltFocStandardInfo;

import java.util.Date;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class FltFocStandInfoSearchVo extends FltFocStandardInfo {

    @ApiModelProperty(value = "页码。如: 1")
    private Integer page;

    @ApiModelProperty(value = "每页数量。 如：10")
    private Integer pageSize;

    @ApiModelProperty(value = "航班日期-开始。如: 2022-02-15")
    private String flightDateStart;

    @ApiModelProperty(value = "航班日期-结束。如: 2022-03-15")
    private String flightDateEnd;



}
