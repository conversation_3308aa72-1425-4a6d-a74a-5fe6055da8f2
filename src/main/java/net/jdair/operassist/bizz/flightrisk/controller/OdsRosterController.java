package net.jdair.operassist.bizz.flightrisk.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnair.opcnet.api.complextype.PageParam;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.domain.FltFocStandardInfo;
import net.jdair.operassist.bizz.flightrisk.domain.OdsRoster;
import net.jdair.operassist.bizz.flightrisk.service.*;
import net.jdair.operassist.bizz.flightrisk.vo.OdsRosterSearchVo;
import net.jdair.operassist.common.esbapi.ESBService;
import net.jdair.operassist.common.exception.BaseException;
import net.jdair.operassist.common.page.TableDataInfo;
import net.jdair.operassist.common.response.RestResponse;
import net.jdair.operassist.common.response.esb.EsbApiResponse;
import net.jdair.operassist.common.response.esb.OdsRosterEsb;
import net.jdair.operassist.common.util.LocalDateUtils;
import net.jdair.operassist.common.util.OdsRouterUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Ods 新机组排班任务书V2 相关Api封装
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/operassist/odsRoster")
public class OdsRosterController {

    @Resource
    private ESBService esbService;
    @Resource
    private OdsRosterService odsRosterService;


    @Autowired
    private ApoiFpaiService apoiFpaiService ;
    @Autowired
    private ApoiFpdiService apoiFpdiService;
    @Autowired
    private MNfmsFmalFctiService mNfmsFmalFctiService;
    @Autowired
    private DesiFparService desiFparService;

    /**
     * 分页查询
     *
     * @param param 参数
     * @return 结果
     */
    @PostMapping(value = "/list")
    public RestResponse<Page<OdsRoster>> listByPage(@RequestBody OdsRosterSearchVo param) {
        if (param.getPage() == null || param.getPageSize() == null) {
            throw new BaseException("缺少必要参数");
        }
        Page<OdsRoster> pageResult = odsRosterService.listByPage(param);
        return RestResponse.ok(new TableDataInfo(pageResult.getRecords(), (int) pageResult.getTotal()));
    }

    /**
     * 查询条件如下：
     * B. 航班日期（当地时间）(fltDate)=当日日期，A. 航班号（flightNo）=B.航班号（fltIds），
     * A.飞机号（acLongNo）=B.飞机号（acNo），A.航线（airlineCode）=B. 航线（routes）。
     *
     * @return 任务书数据
     */
    @PostMapping(value = "/queryCaptain")
    public RestResponse<OdsRoster> queryCaptain(@RequestBody FltFocStandardInfo param) {
        if (StrUtil.isBlank(param.getFlightNo())) {
            throw new BaseException("缺少必要参数");
        }
        if (StrUtil.isBlank(param.getAcLongNo())) {
            throw new BaseException("缺少必要参数");
        }
        if (StrUtil.isBlank(param.getDepStn())) {
            throw new BaseException("缺少必要参数");
        }
        if (StrUtil.isBlank(param.getArrStn())) {
            throw new BaseException("缺少必要参数");
        }

        //根据条件查询机组信息
        OdsRoster roster = odsRosterService.queryCaptain(param);
        //返回结果
        return RestResponse.ok(roster);
    }


    /**
     * 初始化未来30天机组排版任务书数据
     * 调用esb接口，将结果持久化到数据库
     *
     * @return 同步结果
     */
    @PostMapping("/initEsbDataToLocalDb")
    public RestResponse<String> initEsbDataToLocalDb() {
        LocalDate now = LocalDate.now();
        String startDate = now.format(DateTimeFormatter.ofPattern(LocalDateUtils.YYYY_MM_DD));
        String endDate = LocalDateUtils.plusDay(startDate, 30);
        //获取30天的集合
        List<String> dateList = LocalDateUtils.getBetweenDateList(startDate, endDate);
        for (String fltDate : dateList) {
            ApiRequest apiRequest = new ApiRequest();
            EsbApiResponse<OdsRosterEsb> response = new EsbApiResponse<>();
            //根据页数，循环调用接口获取数据
            int pageIndex = 0;
            int pageSize = 500;
            do {
                pageIndex++;
                PageParam pageParam = new PageParam();
                pageParam.setPageIndex(pageIndex);
                pageParam.setPageSize(pageSize);
                apiRequest.setPageParam(pageParam);
                //航班日期
                apiRequest.setOption("fltDate", fltDate);
                try {
                    log.info("同步odsRoster数据查询接口，{}，(第{}页)，开始，参数{}", fltDate, pageIndex, JSON.toJSONString(apiRequest));
                    //查询数据
                    response = esbService.findFlightReportV2(apiRequest);
                    log.info("同步odsRoster数据查询接口，{}，(第{}页)，结束", fltDate, pageIndex);
                    //数据入库
                    int dataSize = response.getData().size();
                    if (dataSize > 0) {
                        //数据量较多，500条一次批量
                        List<OdsRoster> fltFocStandardInfos = OdsRouterUtils.convertToDbEntity(response);
                        odsRosterService.batchInsert(fltFocStandardInfos);
                    }
                } catch (Exception e) {
                    log.error("同步odsRoster数据查询接口，{}，(第{}页)，异常", fltDate, pageIndex);
                    log.error("同步odsRoster数据查询接口异常，{}，，日志：{}，", fltDate, e.getMessage());
                    e.printStackTrace();
                }
            } while ((pageSize * pageIndex) < response.getPageResult().getTotalRecords());
        }
        //返回结果
        return RestResponse.ok("success");
    }

    /**
     * 每个小时同步一次
     *
     * @return 结果
     */
    @PostMapping("/loadEsbDataToLocalDb")
    public RestResponse<String> loadEsbDataToLocalDb() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime localDateTime = now.minusHours(1);
        LocalDateTime start = localDateTime.withMinute(0).withSecond(0);
        LocalDateTime end = localDateTime.withMinute(59).withSecond(59);
        String startDate = start.format(DateTimeFormatter.ofPattern(LocalDateUtils.YYYY_MM_DD_HH_MM_SS));
        String endDate = end.format(DateTimeFormatter.ofPattern(LocalDateUtils.YYYY_MM_DD_HH_MM_SS));
        ApiRequest apiRequest = new ApiRequest();
        EsbApiResponse<OdsRosterEsb> response = new EsbApiResponse<>();
        //根据页数，循环调用接口获取数据
        int pageIndex = 0;
        int pageSize = 500;
        do {
            pageIndex++;
            PageParam pageParam = new PageParam();
            pageParam.setPageIndex(pageIndex);
            pageParam.setPageSize(pageSize);
            apiRequest.setPageParam(pageParam);
            //航班日期
            apiRequest.setOption("odsUpdatedTimeStart", startDate);
            apiRequest.setOption("odsUpdatedTimeEnd", endDate);
            try {
                log.info("同步odsRoster数据查询接口，(第{}页)，开始，参数{}", pageIndex, JSON.toJSONString(apiRequest));
                //查询数据
                response = esbService.findFlightReportV2(apiRequest);
                log.info("roster response : {}",JSON.toJSONString(response));
                log.info("同步odsRoster数据查询接口，(第{}页)，结束", pageIndex);
                //数据入库
                int dataSize = response.getData().size();
                if (dataSize > 0) {
                    //数据量较多，500条一次批量
                    List<OdsRoster> fltFocStandardInfos = OdsRouterUtils.convertToDbEntity(response);
                    odsRosterService.batchInsert(fltFocStandardInfos);
                }
            } catch (Exception e) {
                log.error("同步odsRoster数据查询接口，(第{}页)，异常", pageIndex);
                log.error("同步odsRoster数据查询接口异常，日志：{}，", e.getMessage());
                e.printStackTrace();
            }
        } while ((pageSize * pageIndex) < response.getPageResult().getTotalRecords());

        //返回结果
        return RestResponse.ok("success");
    }



    @RequestMapping("/loadEsbDataToLocalDb_mNfmsFmalFcti")
    public RestResponse<String>   loadEsbDataToLocalDb_mNfmsFmalFcti(@RequestParam(name = "days") String days)throws Exception{

        mNfmsFmalFctiService.loadEsbDataToLocalDb(days);
        return RestResponse.ok("success");
    }

    @RequestMapping("/loadEsbDataToLocalDb_desiFpar")
    public RestResponse<String>   loadEsbDataToLocalDb_desiFpar(@RequestParam(name = "days") String days)throws Exception{
        desiFparService.loadEsbDataToLocalDb(days);

        return RestResponse.ok("success");
    }
    @RequestMapping("/loadEsbDataToLocalDb_apoiFpai")
    public RestResponse<String>   loadEsbDataToLocalDb_apoiFpai(@RequestParam(name = "days") String days)throws Exception{

        apoiFpaiService.loadEsbDataToLocalDb(days);

        return RestResponse.ok("success");
    }
    @RequestMapping("/loadEsbDataToLocalDb_apoiFpdi")
    public RestResponse<String>   loadEsbDataToLocalDb_apoiFpdi(@RequestParam(name = "days") String days)throws Exception{

        apoiFpdiService.loadEsbDataToLocalDb(days);

        return RestResponse.ok("success");
    }





    @ApiOperation(value = "查询 odsRosterApi_findPositionRosterV2 数据", notes = "查询 odsRosterApi_findPositionRosterV2 数据")
    @RequestMapping(value = "/findPositionRosterV2")
    private ApiResponse findPositionRosterV2(@RequestParam Map<String, Object> options ,
                                             ApiRequest apiRequest) {
        Integer pageIndex =0 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }

        log.info("ESB查询esbService.odsRosterApi_findPositionRosterV2 接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = esbService.odsRosterApi_findPositionRosterV2(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询esbService.odsRosterApi_findPositionRosterV2 接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }



    @ApiOperation(value = "查询 odsRosterApi_findStandByRosterV2 数据", notes = "查询 odsRosterApi_findStandByRosterV2 数据")
    @RequestMapping(value = "/findStandByRosterV2")
    private ApiResponse findStandByRosterV2(@RequestParam Map<String, Object> options ,
                                             ApiRequest apiRequest) {
        Integer pageIndex =0 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }

        log.info("ESB查询esbService.odsRosterApi_findStandByRosterV2 接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = esbService.odsRosterApi_findStandByRosterV2(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询esbService.odsRosterApi_findStandByRosterV2 接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }


}
