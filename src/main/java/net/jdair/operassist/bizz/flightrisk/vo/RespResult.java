package net.jdair.operassist.bizz.flightrisk.vo;

//import org.codehaus.jackson.map.annotate.JsonSerialize;
//import org.codehaus.jackson.map.annotate.JsonSerialize.Inclusion;

//@JsonSerialize(include = Inclusion.NON_NULL)
public class RespResult implements java.io.Serializable{
	private String message; //message 调试人员看的Message，仅供开发人员查看 

	private String viewMessage; //viewMessage可以显示给用户看的Msessage，需要国际化 
	
	private String resultCode;//错误代码

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getViewMessage() {
		return viewMessage;
	}

	public void setViewMessage(String viewMessage) {
		this.viewMessage = viewMessage;
	}

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}
	
}
