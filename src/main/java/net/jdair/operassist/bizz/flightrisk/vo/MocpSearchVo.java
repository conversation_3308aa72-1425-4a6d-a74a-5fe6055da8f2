package net.jdair.operassist.bizz.flightrisk.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 */
@Data
public class MocpSearchVo implements Serializable {

    @ApiModelProperty(value = "页码。如: 1")
    private Integer page;

    @ApiModelProperty(value = "每页数量。 如：10")
    private Integer pageSize;

    // 航司	aviation	String  (DRJ)
    private String aviation;

    // MOCP更新日期开始>=	updatedTimeStart	String	格式：yyyy-MM-dd HH:mm:ss
    private String updatedTimeStart;

    // MOCP更新日期结束<=	updatedTimeEnd 	String	格式：yyyy-MM-dd HH:mm:ss
    private String updatedTimeEnd;

    /**
     * 故障状态(0:OPEN,1:CLOSE,2:作废)
     */
    private Integer failureState;

    /**
     * 故障放行状态(0:未完成,1:已完成,2:MEL/CDL放行,3:其他依据放行)
     */
    private String rleaseState;

    /**
     * 事件状态(1:未完成,2:保留OPEN,3:保留CLOSE,4:已完成,5:作废)
     */
    private String eventStatus;

    /**
     * 机号
     */
    private String machineNumber;

    /**
     * 机型
     */
    private String planeModel;

    /**
     * 航站
     */
    private String termin;

    /**
     * 保留依据章节
     */
    private String releaseBasis;

    /**
     * 本地库插入开始时间
     */
    private Date localCreateTimeStart;

    /**
     * 本地库插入截止时间
     */
    private Date localCreateTimeEnd;

    /**
     * 是否发送预警邮件
     */
    private Boolean sendAlertEmail;
}
