package net.jdair.operassist.bizz.flightrisk.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnair.opcnet.api.complextype.PageParam;
import com.hnair.opcnet.api.complextype.PageResult;
import com.hnair.opcnet.api.ods.flt.FltPayloadRequest;
import com.hnair.opcnet.api.ods.flt.FltPayloadResponse;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.hnair.opcnet.api.complextype.Result;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.domain.ApoiFpai;
import net.jdair.operassist.bizz.flightrisk.domain.ApoiFpdi;
import net.jdair.operassist.bizz.flightrisk.domain.DesiFpar;
import net.jdair.operassist.bizz.flightrisk.domain.MNfmsFmalFcti;
import net.jdair.operassist.bizz.flightrisk.service.ApoiFpaiService;
import net.jdair.operassist.bizz.flightrisk.service.ApoiFpdiService;
import net.jdair.operassist.bizz.flightrisk.service.DesiFparService;
import net.jdair.operassist.bizz.flightrisk.service.MNfmsFmalFctiService;
import net.jdair.operassist.common.esbapi.ESBService;
import net.jdair.operassist.common.response.RestResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(value = "CsdApi 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/csdApi")
public class CsdApiController {
    @Resource
    private ESBService esbService;
    @Resource
    private DesiFparService desiFparService;
    @Resource
    private ApoiFpdiService apoiFpdiService;
    @Resource
    private ApoiFpaiService apoiFpaiService;

    @Resource
    private MNfmsFmalFctiService mNfmsFmalFctiService;

    @ApiOperation(value = "查询 csdApi_findDesiFparByPage 数据", notes = "查询 csdApi_findDesiFparByPage 数据")
    @RequestMapping(value = "/findDesiFparByPage")
    private ApiResponse findDesiFparByPage(@RequestParam Map<String, Object> options , ApiRequest apiRequest){
            Integer pageIndex =1 ;
            try{
                if(apiRequest.getPageParam()!=null){
                    pageIndex = apiRequest.getPageParam().getPageIndex();
                };
                apiRequest.getOptions().putAll(options);
            }catch(Exception e){
                log.error("获取分页参数出错",e);
            }

            ApiResponse response =  esbService.csdApi_findDesiFparByPage(apiRequest);
            return response ;
    }

    @ApiOperation(value = "查询 csdApi_findApoiFpdiByPage 数据", notes = "查询 csdApi_findApoiFpdiByPage 数据")
    @RequestMapping(value = "/findApoiFpdiByPage")
    private ApiResponse findApoiFpdiByPage(@RequestParam Map<String, Object> options ,ApiRequest apiRequest){
        Integer pageIndex =0 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }
        ApiResponse response =  esbService.csdApi_findApoiFpdiByPage(apiRequest);
        return response ;
    }

    @ApiOperation(value = "查询 csdApi_findApoiFpaiByPage 数据", notes = "查询 csdApi_findApoiFpaiByPage 数据")
    @RequestMapping(value = "/findApoiFpaiByPage")
    private ApiResponse findApoiFpaiByPage(@RequestParam Map<String, Object> options ,ApiRequest apiRequest){
        Integer pageIndex =0 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }
        ApiResponse response =  esbService.csdApi_findApoiFpaiByPage(apiRequest);
        return response ;
    }


    @ApiOperation("定时更新数据")
    @PostMapping(value = "/loadEsbDataToLocalDb_DesiFpar")
    public RestResponse<String> loadEsbDataToLocalDb_DesiFpar(@RequestParam(name = "days") String days)throws Exception {
            desiFparService.loadEsbDataToLocalDb(days);
            //返回结果
            return RestResponse.ok("success");

    }


    @ApiOperation(value = "查询 csdApi_findDesiFparByPage 数据从中台缓存获得", notes = "查询 csdApi_findDesiFparByPage 数据")
    @RequestMapping(value = "/findDesiFparByPage_cache")
    private ApiResponse findDesiFparByPage_cache(@RequestParam Map<String, Object> options , ApiRequest apiRequest){
        Page<DesiFpar> page = new Page<>();
        Integer pageIndex =1 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
                if(pageIndex!=null){
                 page.setCurrent(pageIndex);
                }
                Integer pageSize = apiRequest.getPageParam().getPageSize();
                if(pageSize!=null){
                  page.setSize(pageSize);
                }else{
                    page.setSize(20);
                }
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }

         Page<DesiFpar> desiFparPage = desiFparService.findDesiFparByPage_cache(options,page);

//        ApiResponse response =  esbService.csdApi_findDesiFparByPage(apiRequest);

        ApiResponse response = new ApiResponse();

        for(DesiFpar desiFpar :  desiFparPage.getRecords()){
            Map<String,Object>  map =  BeanUtil.beanToMap(desiFpar);
            response.getData().add(map);
        }
        PageResult pageResult = new PageResult();
                   pageResult.setTotalRecords((int)desiFparPage.getTotal());
                   pageResult.setPageIndex((int)desiFparPage.getCurrent());
                   pageResult.setPageSize((int)desiFparPage.getSize());
                   pageResult.setTotalPages((int)desiFparPage.getPages());

                   pageResult.setPageRecords(desiFparPage.getRecords().size());

        response.setPageResult(pageResult);
        Result result = new Result();
        result.setResultCode(1);
        result.setResultMsg("调用成功");
        response.setResult(result);
        return response ;
    }






    @ApiOperation("定时更新数据")
    @PostMapping(value = "/loadEsbDataToLocalDb_ApoiFpdi")
    public RestResponse<String> loadEsbDataToLocalDb_ApoiFpdi(@RequestParam(name = "days") String days)throws Exception {
        apoiFpdiService.loadEsbDataToLocalDb(days);
        //返回结果
        return RestResponse.ok("success");

    }


    @ApiOperation(value = "查询 csdApi_findApoiFpdiByPage 数据从中台缓存获得", notes = "查询 csdApi_findApoiFpdiByPage 数据")
    @RequestMapping(value = "/findApoiFpdiByPage_cache")
    private ApiResponse findApoiFpdiByPage_cache(@RequestParam Map<String, Object> options , ApiRequest apiRequest){
        Page<DesiFpar> page = new Page<>();
        Integer pageIndex =1 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
                if(pageIndex!=null){
                    page.setCurrent(pageIndex);
                }
                Integer pageSize = apiRequest.getPageParam().getPageSize();
                if(pageSize!=null){
                    page.setSize(pageSize);
                }else{
                    page.setSize(20);
                }
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }

        Page<ApoiFpdi> desiFparPage = apoiFpdiService.findApoiFpdiByPage_cache(options,page);


        ApiResponse response = new ApiResponse();

        for(ApoiFpdi apoiFpdi :  desiFparPage.getRecords()){
           // apoiFpdi.getId();
            Map<String,Object>  map =  BeanUtil.beanToMap(apoiFpdi);
            response.getData().add(map);
        }
        PageResult pageResult = new PageResult();
        pageResult.setTotalRecords((int)desiFparPage.getTotal());
        pageResult.setPageIndex((int)desiFparPage.getCurrent());
        pageResult.setPageSize((int)desiFparPage.getSize());
        pageResult.setTotalPages((int)desiFparPage.getPages());

        pageResult.setPageRecords(desiFparPage.getRecords().size());

        response.setPageResult(pageResult);
        Result result = new Result();
        result.setResultCode(1);
        result.setResultMsg("调用成功");
        response.setResult(result);
        return response ;
    }






    @ApiOperation("定时更新数据")
    @PostMapping(value = "/loadEsbDataToLocalDb_ApoiFpai")
    public RestResponse<String> loadEsbDataToLocalDb_ApoiFpai(@RequestParam(name = "days") String days)throws Exception {
        apoiFpaiService.loadEsbDataToLocalDb(days);
        //返回结果
        return RestResponse.ok("success");

    }


    @ApiOperation(value = "查询 csdApi_findApoiFpaiByPage 数据从中台缓存获得", notes = "查询 csdApi_findApoiFpaiByPage 数据")
    @RequestMapping(value = "/findApoiFpaiByPage_cache")
    private ApiResponse findApoiFpaiByPage_cache(@RequestParam Map<String, Object> options , ApiRequest apiRequest){
        Page<DesiFpar> page = new Page<>();
        Integer pageIndex =1 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
                if(pageIndex!=null){
                    page.setCurrent(pageIndex);
                }
                Integer pageSize = apiRequest.getPageParam().getPageSize();
                if(pageSize!=null){
                    page.setSize(pageSize);
                }else{
                    page.setSize(20);
                }
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }

        Page<ApoiFpai> desiFparPage = apoiFpaiService.findApoiFpaiByPage_cache(options,page);


        ApiResponse response = new ApiResponse();

        for(ApoiFpai apoiFpai :  desiFparPage.getRecords()){
            Map<String,Object>  map =  BeanUtil.beanToMap(apoiFpai);
            response.getData().add(map);
        }
        PageResult pageResult = new PageResult();
        pageResult.setTotalRecords((int)desiFparPage.getTotal());
        pageResult.setPageIndex((int)desiFparPage.getCurrent());
        pageResult.setPageSize((int)desiFparPage.getSize());
        pageResult.setTotalPages((int)desiFparPage.getPages());

        pageResult.setPageRecords(desiFparPage.getRecords().size());

        response.setPageResult(pageResult);
        Result result = new Result();
        result.setResultCode(1);
        result.setResultMsg("调用成功");
        response.setResult(result);
        return response ;
    }




    /**
     *用  MNfnsApiController.loadEsbDataToLocalDb_MNfmsFmalFcti 替代
     * **/
    @ApiOperation("定时更新数据")
    @PostMapping(value = "/loadEsbDataToLocalDb_MNfmsFmalFcti")
    @Deprecated
    public RestResponse<String> loadEsbDataToLocalDb_MNfmsFmalFcti(@RequestParam(name = "days") String days)throws Exception {
        mNfmsFmalFctiService.loadEsbDataToLocalDb(days);
        //返回结果
        return RestResponse.ok("success");

    }





}
