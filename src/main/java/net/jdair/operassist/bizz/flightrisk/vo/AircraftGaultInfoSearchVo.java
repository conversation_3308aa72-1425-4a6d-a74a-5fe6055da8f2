package net.jdair.operassist.bizz.flightrisk.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询对象
 */
@Data
public class AircraftGaultInfoSearchVo implements Serializable {

    @ApiModelProperty(value = "页码。如: 1")
    private Integer page;

    @ApiModelProperty(value = "每页数量。 如：10")
    private Integer pageSize;

    // 源表id	srcId	String	例如：8MgALQYcNHCu4MohyOH
    private String srcId;

    // 飞机号	aircraftNo	String	例如：1658或B1037
    private String aircraftNo;

    // 创建时间>= srcCreateTimeStart	String	格式：yyyy-MM-dd HH:mm:ss 例如：2018-05-25 15:26:52
    private String srcCreateTimeStart;

    // 创建时间<= srcCreateTimeEnd	String	格式：yyyy-MM-dd HH:mm:ss 例如：2018-05-25 15:26:52
    private String srcCreateTimeEnd;

    // 修改时间>= srcUpdateTimeStart	String	格式：yyyy-MM-dd HH:mm:ss 例如：2018-05-25 15:26:52
    private String srcUpdateTimeStart;

    // 修改时间<= srcUpdateTimeEnd	String	格式：yyyy-MM-dd HH:mm:ss 例如：2018-05-25 15:26:52
    private String srcUpdateTimeEnd;

    // 机场三字码	iataId	String	例如：HAK
    private String iataId;

    // ODS更新时间>=	updatedTimeStart	String	格式：yyyy-MM-dd HH:mm:ss 例如：2018-05-25 15:26:52
    private String updatedTimeStart;

    // ODS更新时间<=	updatedTimeEnd	String	格式：yyyy-MM-dd HH:mm:ss 例如：2018-05-25 15:26:52
    private String updatedTimeEnd;

    // 删除标识	deleteds	String	不传则默认返回deleted为0的记录，以逗号分隔多个取值，如0,1； 0代表未删除，1代表删除
    private String deleteds;

}
