package net.jdair.operassist.bizz.flightrisk.controller;

import com.hnair.opcnet.api.ods.ap.AirportFenxiInfoPageRequest;
import com.hnair.opcnet.api.ods.ap.AirportFenxiInfoPageResponse;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.common.esbapi.ESBService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(value = "ApAirportFenxiApi 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/ApAirportFenxiApi")
public class ApAirportFenxiApiController {

    @Autowired
    private ESBService esbService;
    @ApiOperation(value = "查询 getAirportFenxiInfoByPage 数据", notes = "查询 getAirportFenxiInfoByPage 数据")
    @RequestMapping(value = "/getAirportFenxiInfoByPage")
    private AirportFenxiInfoPageResponse getAirportFenxiInfoByPage(@RequestParam Map<String, Object> options ,
                                                                   AirportFenxiInfoPageRequest airportFenxiInfoPageRequest){

         AirportFenxiInfoPageResponse response =  esbService.getAirportFenxiInfoByPage(airportFenxiInfoPageRequest);
         return response ;

    }


}
