package net.jdair.operassist.bizz.flightrisk.controller;

import com.hnair.opcnet.api.ods.wi.GetWeatherMessageRequest;
import com.hnair.opcnet.api.ods.wi.GetWeatherMessageResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.common.esbapi.ESBService;
import net.jdair.operassist.common.response.RestResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * <p>
 * 天气信息
 * </p>
 *
 */
@Api(value = "WeatherMessages 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/WeatherMessages")
public class WeatherMessagesController {

    @Resource
    private ESBService esbService;

    /**
     * 查询列表数据
     */
    @ApiOperation(value = "查询 WeatherMessages 列表数据", notes = "查询 WeatherMessages 列表数据")
    @PostMapping(value = "/list")
    public RestResponse<GetWeatherMessageResponse> listByPage(@RequestBody GetWeatherMessageRequest apiRequest) {
        GetWeatherMessageResponse apiResponse = esbService.getWeatherMessages(apiRequest);
        return RestResponse.ok(apiResponse);
    }

}
