package net.jdair.operassist.bizz.flightrisk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.jdair.operassist.bizz.flightrisk.domain.MNfmsFmalFcti;

import java.util.Date;
import java.util.Map;

public interface MNfmsFmalFctiService {


    public Date maxUpdateTime();

    public void loadEsbDataToLocalDb(String days)throws Exception;

    Page<MNfmsFmalFcti> findMNfmsFmalFctiByPage_cache(Map<String, Object> options, Page page);
}
