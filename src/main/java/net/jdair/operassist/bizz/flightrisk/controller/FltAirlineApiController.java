package net.jdair.operassist.bizz.flightrisk.controller;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONArray;
import com.hnair.opcnet.api.ods.flt.*;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.common.esbapi.ESBService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Api(value = " com.hnair.opcnet.api.ods.flt.FltAirlineApi 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/FltAirlineApi")
public class FltAirlineApiController {

    @Resource
    private ESBService esbService;

     @ApiOperation(value = "查询 fltAirlineApi_getFltPayload 数据", notes = "查询 fltAirlineApi_getFltPayload 数据")
     @RequestMapping(value = "/getFltPayload")
     private FltPayloadResponse getFltPayload(FltPayloadRequest apiRequest){
         FltPayloadResponse  fltPayloadResponse =  esbService.fltAirlineApi_getFltPayload(apiRequest);

         return fltPayloadResponse ;

     }


    @ApiOperation(value = "查询 fltAirlineApi_getFltAirline 数据", notes = "查询 fltAirlineApi_getFltAirline 数据")
    @RequestMapping(value = "/getFltAirline")
    private FltAirlineResponse getFltAirline(FltAirlineRequest apiRequest){

            FltAirlineResponse fltPayloadResponse =  esbService.fltAirlineApi_getFltAirline(apiRequest);

            return             fltPayloadResponse ;

    }




    @ApiOperation(value = "查询 fltAirlineApi_getAirlineSpecialByPage 数据", notes = "查询 fltAirlineApi_getAirlineSpecialByPage 数据")
    @RequestMapping(value = "/getAirlineSpecialByPage")
    private ApiResponse getAirlineSpecialByPage(
            @RequestParam Map<String, Object> options ,
            ApiRequest apiRequest){
        try{
             apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }

        log.info("ESB查询esbService.fltAirlineApi_getAirlineSpecialByPage接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse airlineSpecial   =  esbService.fltAirlineApi_getAirlineSpecialByPage(apiRequest);
        long end   = System.currentTimeMillis();
        log.info("ESB查询esbService.fltAirlineApi_getAirlineSpecialByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(airlineSpecial), end - start);

        return      airlineSpecial ;

    }


    @ApiOperation(value = "查询 fltAirlineApi_getFleAirlineByPage 数据", notes = "查询 fltAirlineApi_getFleAirlineByPage 数据")
    @RequestMapping(value = "/getFleAirlineByPage")
    private FltAirlinePageResponse getFleAirlineByPage(
            @RequestParam Map<String, Object> options ,
            FltAirlinePageRequest apiRequest){
//        try{
//            apiRequest.getOptions().putAll(options);
//        }catch(Exception e){
//            log.error("获取分页参数出错",e);
//        }

//          apiRequest.setPageNo();
//          apiRequest.setPageSize();

//        pageParam.orderBy=id&
//        pageParam.orderDir=ASC&
//        pageParam.pageSize=1000&
//        pageParam.pageIndex=1

//        MapUtil.getStr(options,"pageParam.orderBy");
//        MapUtil.getStr(options,"pageParam.orderDir");
        Integer pageSize = MapUtil.getInt(options,"pageParam.pageSize");
        if(pageSize!=null){
            apiRequest.setPageSize(pageSize);
        }
        Integer pageIndex = MapUtil.getInt(options,"pageParam.pageIndex");
        if(pageIndex!=null){
            apiRequest.setPageNo(pageIndex);
        }

        log.info("ESB查询esbService.fltAirlineApi_getFleAirlineByPage接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        FltAirlinePageResponse airlineSpecial   =  esbService.fltAirlineApi_getFleAirlineByPage(apiRequest);
        long end   = System.currentTimeMillis();
        log.info("ESB查询esbService.fltAirlineApi_getFleAirlineByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(airlineSpecial), end - start);

        return      airlineSpecial ;

    }

}
