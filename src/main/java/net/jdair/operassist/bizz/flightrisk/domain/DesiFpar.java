package net.jdair.operassist.bizz.flightrisk.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("航班计划调整信息")
@TableName("flt_operassist_api_desifpar")
@Data
public class DesiFpar {
    @ApiModelProperty("主键")
    @TableField("id")
    @TableId
    private Long id;//主键
    @ApiModelProperty("源表ID")
    @TableField("csdId")
    private String csdId;//源表ID
    @ApiModelProperty("CSD_MSG_XML表ID主键")
    @TableField("srcId")
    private String srcId;//CSD_MSG_XML表ID主键
    @ApiModelProperty("航空器识别标志")
    @TableField("callsign")
    private String callsign;//航空器识别标志
    @ApiModelProperty("航班号")
    @TableField("flightNo")
    private String flightNo;//航班号
    @ApiModelProperty("航班日期-北京")
    @TableField("datopChn")
    private String datopChn;//航班日期-北京
    @ApiModelProperty("共享单位航班标识符")
    @TableField("unitufi")
    private String unitufi;//共享单位航班标识符
    @ApiModelProperty("航班唯一标识符")
    @TableField("gufi")
    private String gufi;//航班唯一标识符
    @ApiModelProperty("预先飞行计划标识符")
    @TableField("preflightid")
    private String preflightid;//预先飞行计划标识符
    @ApiModelProperty("航空器注册号")
    @TableField("regnumber")
    private String regnumber;//航空器注册号
    @ApiModelProperty("航空器地址码")
    @TableField("addresscode")
    private String addresscode;//航空器地址码
    @ApiModelProperty("计划离港时间")
    @TableField("sobt")
    private String sobt;//计划离港时间
    @ApiModelProperty("计划到港时间")
    @TableField("sibt")
    private String sibt;//计划到港时间
    @ApiModelProperty("计划起飞机场")
    @TableField("depap")
    private String depap;//计划起飞机场
    @ApiModelProperty("计划目的地机场")
    @TableField("arrap")
    private String arrap;//计划目的地机场
    @ApiModelProperty("计划起飞机场(三字码)")
    @TableField("depapT")
    private String depapT;//计划起飞机场(三字码)
    @ApiModelProperty("计划目的地机场(三字码)")
    @TableField("arrapT")
    private String arrapT;//计划目的地机场(三字码)
    @ApiModelProperty("预计撤轮档时间")
    @TableField("eobt")
    private String eobt;//预计撤轮档时间
    @ApiModelProperty("原航空器识别标志")
    @TableField("sourceCallsign")
    private String sourceCallsign;//原航空器识别标志
    @ApiModelProperty("原共享单位航班标识符")
    @TableField("sourceUnitufi")
    private String sourceUnitufi;//原共享单位航班标识符
    @ApiModelProperty("原航班唯一标识符")
    @TableField("sourceGufi")
    private String sourceGufi;//原航班唯一标识符
    @ApiModelProperty("原预先飞行计划标识符")
    @TableField("sourcePreflightid")
    private String sourcePreflightid;//原预先飞行计划标识符
    @ApiModelProperty("原航空器注册号")
    @TableField("sourceRegnumber")
    private String sourceRegnumber;//原航空器注册号
    @ApiModelProperty("原航空器地址码")
    @TableField("sourceAddresscode")
    private String sourceAddresscode;//原航空器地址码
    @ApiModelProperty("原计划离港时间")
    @TableField("sourceSobt")
    private String sourceSobt;//原计划离港时间
    @ApiModelProperty("原计划到港时间")
    @TableField("sourceSibt")
    private String sourceSibt;//原计划到港时间
    @ApiModelProperty("原计划起飞机场")
    @TableField("sourceDepap")
    private String sourceDepap;//原计划起飞机场
    @ApiModelProperty("原计划目的地机场")
    @TableField("sourceArrap")
    private String sourceArrap;//原计划目的地机场
    @ApiModelProperty("原预计撤轮档时间")
    @TableField("sourceEobt")
    private String sourceEobt;//原预计撤轮档时间
    @ApiModelProperty("预先飞行计划时刻调整状态")
    @TableField("scheduleAdjustApply")
    private String scheduleAdjustApply;//预先飞行计划时刻调整状态
    @ApiModelProperty("补班计划状态")
    @TableField("scheduleRpApply")
    private String scheduleRpApply;//补班计划状态
    @ApiModelProperty("计划取消状态")
    @TableField("scheduleCnlApply")
    private String scheduleCnlApply;//计划取消状态
    @ApiModelProperty("不满足申请时间要求")
    @TableField("isNotNormalCal")
    private String isNotNormalCal;//不满足申请时间要求
    @ApiModelProperty("预先飞行计划调整申请时")
    @TableField("applicationTime")
    private Date applicationTime;//预先飞行计划调整申请时
    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;//备注
    @ApiModelProperty("来源系统")
    @TableField("sourcesystemId")
    private String sourcesystemId;//来源系统
    @ApiModelProperty("发送时间")
    @TableField("sendTime")
    private Date sendTime;//发送时间
    @ApiModelProperty("最早原计划离港时间")
    @TableField("srcStd")
    private String srcStd;//最早原计划离港时间
    @ApiModelProperty("源创建时间")
    @TableField("srcCreatedTime")
    private Date srcCreatedTime;//源创建时间
    @ApiModelProperty("源更新时间")
    @TableField("srcUpdatedTime")
    private Date srcUpdatedTime;//源更新时间
    @ApiModelProperty("ODS创建时间")
    @TableField("createdTime")
    private Date createdTime;//ODS创建时间
    @ApiModelProperty("ODS更新时间")
    @TableField("updatedTime")
    private Date updatedTime;//ODS更新时间
    @ApiModelProperty("JOB批次号，ETL处理")
    @TableField("batchJobNo")
    private String batchJobNo;//JOB批次号，ETL处理

}
