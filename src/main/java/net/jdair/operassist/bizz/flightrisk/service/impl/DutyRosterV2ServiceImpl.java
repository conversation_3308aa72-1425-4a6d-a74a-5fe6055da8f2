package net.jdair.operassist.bizz.flightrisk.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnair.opcnet.api.complextype.PageParam;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.domain.DutyRosterV2;
import net.jdair.operassist.bizz.flightrisk.mapper.DutyRosterV2Mapper;
import net.jdair.operassist.bizz.flightrisk.service.DutyRosterV2Service;
import net.jdair.operassist.common.esbapi.ESBService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class DutyRosterV2ServiceImpl extends ServiceImpl<DutyRosterV2Mapper, DutyRosterV2>
        implements DutyRosterV2Service {

    @Autowired
    private DutyRosterV2Mapper dutyRosterV2Mapper;
    @Autowired
    private ESBService         esbService;
    @Transactional
    @Override
//加载昨日数据到数据库
    public int   loadEsbDataToLocalDb_dutyRosterV2(String date) {
        ApiRequest apiRequest = new ApiRequest();
        if(StrUtil.isNotBlank(date)) {
            apiRequest.setOption("startDate", date);
            apiRequest.setOption("endDate"  , date);
        }else{
            apiRequest.setOption("startDate", DateUtil.formatDate(DateUtil.yesterday()));
            apiRequest.setOption("endDate", DateUtil.formatDate(DateUtil.yesterday()));
        }
        PageParam p = new PageParam();
        p.setPageSize(40);
        p.setOrderBy("id");
        p.setOrderDir("DESC");
        apiRequest.setPageParam(p);
        int pageIndex = 1;
        int totalPages = 1;
        for (pageIndex = 1; pageIndex <= totalPages; pageIndex++) {
            p.setPageIndex(pageIndex);
            apiRequest.setPageParam(p);
            ApiResponse apiResponse = esbService.odsRosterApi_getDutyRosterV2(apiRequest);
            if (pageIndex == 1) {
                totalPages = apiResponse.getPageResult().getTotalPages();
            }
            //TODO 入库排班数据
            List<Map<String, Object>> data = apiResponse.getData();

            for (Map<String, Object> entry : data) {
                DutyRosterV2 dutyRosterV2 = BeanUtil.mapToBean(entry, DutyRosterV2.class, false);
                QueryWrapper<DutyRosterV2> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("flightDate", dutyRosterV2.getFlightDate());
                queryWrapper.eq("flightNo", dutyRosterV2.getFlightNo());
                queryWrapper.eq("sector", dutyRosterV2.getSector());
                queryWrapper.eq("staffId", dutyRosterV2.getStaffId());
                List<DutyRosterV2> dutyRosterV2s = dutyRosterV2Mapper.selectList(queryWrapper);
                if (dutyRosterV2s != null && dutyRosterV2s.size() <= 0) {
                    save(dutyRosterV2);
                } else if (dutyRosterV2s.size() > 0) {
                    DutyRosterV2 dutyRosterV2_db = dutyRosterV2s.get(0);
                    BeanUtil.copyProperties(dutyRosterV2, dutyRosterV2_db, "id");
                    updateById(dutyRosterV2);
                }
            }
        }

        return 0;
    }

    @Override
    public IPage<DutyRosterV2> getDutyRosterV2_db(Page<DutyRosterV2> resultPage, QueryWrapper<DutyRosterV2> queryp){

       return    dutyRosterV2Mapper.selectPage(resultPage,queryp);
    };
}
