package net.jdair.operassist.bizz.flightrisk.vo;

public class FlightReportBean {
	/** * 航班号 */
	private String flightNos;
	/** * 航班日期 */
	private String flightDate;
	/** * 飞机号*/
	private String acNo;
	/** * 起飞日前*/
	private String depTimes;
	/** * 航线号*/
	private String routes;
	/** * 机型 */
	private String aircraftType;
	//	报务教员	
	private String maintainStn;	
	//	备注	
	private String cnRoutes;	
	//	航班性质	
	private String stcs;	
	//	飞行小时	
	private String flyTimes;	
	//	机型	
	private String accd;	
	//	机长	
	private String captain;	
	//	机长员工编号	
	private String captainStaffNo;	
	//	检查员 （飞行）	
	private String pilotChecker;	
	//	检查员员工编号 （飞行）	
	private String pilotCheckerStaffNo;	
	//	学员 （飞行）	
	private String inspectedPilot;	
	//	学员员工编号（飞行）	
	private String inspectedPilotStaffNo;	
	//	检查类别(飞行)	
	private String checkType;	
	//	资深副驾驶	
	private String skipper1;	
	//	资深副驾驶员工编号	
	private String skipper1StaffNo;	
	//	资深副驾驶备注	
	private String skipper1Remark;	
	//	副驾驶	
	private String firstVice1;	
	//	副驾驶员工编号	
	private String firstVice1StaffNo;	
	//	副驾驶备注	
	private String firstVice1Remark;	
	//	非机组人员（飞行)	
	private String accompany1;	
	//	非机组人员ID（飞行)	
	private String accompany1StaffNo;	
	//	执行航班加机组（飞行）	
	private String pilotDeadheadOps;	
	//	执行航班加机组员工编号（飞行）	
	private String pilotDeadheadOpsStaffNo;	
	//	非执行航班加机组（飞行）	
	private String pilotDeadheadOther;	
	//	非执行航班加机组员工编号（飞行）	
	private String pilotDeadheadOtherStaffNo;	
	//	乘务长	
	private String chiefStewardCaptain;	
	//	乘务长员工编号	
	private String chiefStewardCaptainStaffNo;	
	//	乘务长备注	
	private String chiefStewardCaptainRemark;	
	//	乘务员	
	private String stewardCap1;	
	//	乘务员员工编号	
	private String stewardCap1StaffNo;	
	//	乘务员备注	
	private String stewardCap1Remark;	
	//	安全员	
	private String safer1;	
	//	乘务学员	
	private String stewards;	
	//	乘务学员员工编号	
	private String stewardsStaffNo;	
	//	检查员 （乘务）	
	private String stewardChecker;	
	//	检查员员工编号（乘务）	
	private String stewardCheckerStaffNo;	
	//	教员（乘务）	
	private String stewardInstructor;	
	//	教员员工编号（乘务）	
	private String stewardInstructorStaffNo;	
	//	检查类别（乘务）	
	private String stewardCheckType;	
	//	执行航班加机组（乘务）	
	private String stewardDeadheadOps;	
	//	执行航班加机组员工编号（乘务）	
	private String stewardDeadheadOpsStaffNo;	
	//	非执行航班加机组（乘务）	
	private String stewardDeadheadOther;	
	//	非执行航班加机组员工编号（乘务）	
	private String stewardDeadheadOtherStaffNo;	
	//	非机组加机组（乘务）	
	private String jumpSeating;	
	//	非机组加机组员工编号（乘务）	
	private String jumpSeatingStaffNo;	
	//	机组名单（飞行）	
	private String crwPilotInf;	
	//	机组名单（乘务）	
	private String crwStewardInf;	
	//	航班起飞日期	
	private String std;
	//	安全员员工编号	
	private String safer1StaffNo;	
	//	机长备注	
	private String captainRemark;	
	//	银湖系统的公司编码	
	private String companyCode;	
	//	公司名称	
	private String companyName;	
	//	银湖系统的公司ID	
	private String companyId;	
	//	签发日期	
	private String signatureDate;	
	//	签发人链接	
	private String signatureImageUrl;	
	//	到达时间	
	private String arrTimes;	
	//	签派员信息	
	private String dispatcher;	
	//	机务跟机信息	
	private String engineer;
	//	源系统修改时间	
	private String moddt;
	public String getFlightNos() {
		return flightNos;
	}
	public void setFlightNos(String flightNos) {
		this.flightNos = flightNos;
	}
	public String getFlightDate() {
		return flightDate;
	}
	public void setFlightDate(String flightDate) {
		this.flightDate = flightDate;
	}
	public String getAcNo() {
		return acNo;
	}
	public void setAcNo(String acNo) {
		this.acNo = acNo;
	}
	public String getDepTimes() {
		return depTimes;
	}
	public void setDepTimes(String depTimes) {
		this.depTimes = depTimes;
	}
	public String getRoutes() {
		return routes;
	}
	public void setRoutes(String routes) {
		this.routes = routes;
	}
	public String getAircraftType() {
		return aircraftType;
	}
	public void setAircraftType(String aircraftType) {
		this.aircraftType = aircraftType;
	}
	public String getMaintainStn() {
		return maintainStn;
	}
	public void setMaintainStn(String maintainStn) {
		this.maintainStn = maintainStn;
	}
	public String getCnRoutes() {
		return cnRoutes;
	}
	public void setCnRoutes(String cnRoutes) {
		this.cnRoutes = cnRoutes;
	}
	public String getStcs() {
		return stcs;
	}
	public void setStcs(String stcs) {
		this.stcs = stcs;
	}
	public String getFlyTimes() {
		return flyTimes;
	}
	public void setFlyTimes(String flyTimes) {
		this.flyTimes = flyTimes;
	}
	public String getAccd() {
		return accd;
	}
	public void setAccd(String accd) {
		this.accd = accd;
	}
	public String getCaptain() {
		return captain;
	}
	public void setCaptain(String captain) {
		this.captain = captain;
	}
	public String getCaptainStaffNo() {
		return captainStaffNo;
	}
	public void setCaptainStaffNo(String captainStaffNo) {
		this.captainStaffNo = captainStaffNo;
	}
	public String getPilotChecker() {
		return pilotChecker;
	}
	public void setPilotChecker(String pilotChecker) {
		this.pilotChecker = pilotChecker;
	}
	public String getPilotCheckerStaffNo() {
		return pilotCheckerStaffNo;
	}
	public void setPilotCheckerStaffNo(String pilotCheckerStaffNo) {
		this.pilotCheckerStaffNo = pilotCheckerStaffNo;
	}
	public String getInspectedPilot() {
		return inspectedPilot;
	}
	public void setInspectedPilot(String inspectedPilot) {
		this.inspectedPilot = inspectedPilot;
	}
	public String getInspectedPilotStaffNo() {
		return inspectedPilotStaffNo;
	}
	public void setInspectedPilotStaffNo(String inspectedPilotStaffNo) {
		this.inspectedPilotStaffNo = inspectedPilotStaffNo;
	}
	public String getCheckType() {
		return checkType;
	}
	public void setCheckType(String checkType) {
		this.checkType = checkType;
	}
	public String getSkipper1() {
		return skipper1;
	}
	public void setSkipper1(String skipper1) {
		this.skipper1 = skipper1;
	}
	public String getSkipper1StaffNo() {
		return skipper1StaffNo;
	}
	public void setSkipper1StaffNo(String skipper1StaffNo) {
		this.skipper1StaffNo = skipper1StaffNo;
	}
	public String getSkipper1Remark() {
		return skipper1Remark;
	}
	public void setSkipper1Remark(String skipper1Remark) {
		this.skipper1Remark = skipper1Remark;
	}
	public String getFirstVice1() {
		return firstVice1;
	}
	public void setFirstVice1(String firstVice1) {
		this.firstVice1 = firstVice1;
	}
	public String getFirstVice1StaffNo() {
		return firstVice1StaffNo;
	}
	public void setFirstVice1StaffNo(String firstVice1StaffNo) {
		this.firstVice1StaffNo = firstVice1StaffNo;
	}
	public String getFirstVice1Remark() {
		return firstVice1Remark;
	}
	public void setFirstVice1Remark(String firstVice1Remark) {
		this.firstVice1Remark = firstVice1Remark;
	}
	public String getAccompany1() {
		return accompany1;
	}
	public void setAccompany1(String accompany1) {
		this.accompany1 = accompany1;
	}
	public String getAccompany1StaffNo() {
		return accompany1StaffNo;
	}
	public void setAccompany1StaffNo(String accompany1StaffNo) {
		this.accompany1StaffNo = accompany1StaffNo;
	}
	public String getPilotDeadheadOps() {
		return pilotDeadheadOps;
	}
	public void setPilotDeadheadOps(String pilotDeadheadOps) {
		this.pilotDeadheadOps = pilotDeadheadOps;
	}
	public String getPilotDeadheadOpsStaffNo() {
		return pilotDeadheadOpsStaffNo;
	}
	public void setPilotDeadheadOpsStaffNo(String pilotDeadheadOpsStaffNo) {
		this.pilotDeadheadOpsStaffNo = pilotDeadheadOpsStaffNo;
	}
	public String getPilotDeadheadOther() {
		return pilotDeadheadOther;
	}
	public void setPilotDeadheadOther(String pilotDeadheadOther) {
		this.pilotDeadheadOther = pilotDeadheadOther;
	}
	public String getPilotDeadheadOtherStaffNo() {
		return pilotDeadheadOtherStaffNo;
	}
	public void setPilotDeadheadOtherStaffNo(String pilotDeadheadOtherStaffNo) {
		this.pilotDeadheadOtherStaffNo = pilotDeadheadOtherStaffNo;
	}
	public String getChiefStewardCaptain() {
		return chiefStewardCaptain;
	}
	public void setChiefStewardCaptain(String chiefStewardCaptain) {
		this.chiefStewardCaptain = chiefStewardCaptain;
	}
	public String getChiefStewardCaptainStaffNo() {
		return chiefStewardCaptainStaffNo;
	}
	public void setChiefStewardCaptainStaffNo(String chiefStewardCaptainStaffNo) {
		this.chiefStewardCaptainStaffNo = chiefStewardCaptainStaffNo;
	}
	public String getChiefStewardCaptainRemark() {
		return chiefStewardCaptainRemark;
	}
	public void setChiefStewardCaptainRemark(String chiefStewardCaptainRemark) {
		this.chiefStewardCaptainRemark = chiefStewardCaptainRemark;
	}
	public String getStewardCap1() {
		return stewardCap1;
	}
	public void setStewardCap1(String stewardCap1) {
		this.stewardCap1 = stewardCap1;
	}
	public String getStewardCap1StaffNo() {
		return stewardCap1StaffNo;
	}
	public void setStewardCap1StaffNo(String stewardCap1StaffNo) {
		this.stewardCap1StaffNo = stewardCap1StaffNo;
	}
	public String getStewardCap1Remark() {
		return stewardCap1Remark;
	}
	public void setStewardCap1Remark(String stewardCap1Remark) {
		this.stewardCap1Remark = stewardCap1Remark;
	}
	public String getSafer1() {
		return safer1;
	}
	public void setSafer1(String safer1) {
		this.safer1 = safer1;
	}
	public String getStewards() {
		return stewards;
	}
	public void setStewards(String stewards) {
		this.stewards = stewards;
	}
	public String getStewardsStaffNo() {
		return stewardsStaffNo;
	}
	public void setStewardsStaffNo(String stewardsStaffNo) {
		this.stewardsStaffNo = stewardsStaffNo;
	}
	public String getStewardChecker() {
		return stewardChecker;
	}
	public void setStewardChecker(String stewardChecker) {
		this.stewardChecker = stewardChecker;
	}
	public String getStewardCheckerStaffNo() {
		return stewardCheckerStaffNo;
	}
	public void setStewardCheckerStaffNo(String stewardCheckerStaffNo) {
		this.stewardCheckerStaffNo = stewardCheckerStaffNo;
	}
	public String getStewardInstructor() {
		return stewardInstructor;
	}
	public void setStewardInstructor(String stewardInstructor) {
		this.stewardInstructor = stewardInstructor;
	}
	public String getStewardInstructorStaffNo() {
		return stewardInstructorStaffNo;
	}
	public void setStewardInstructorStaffNo(String stewardInstructorStaffNo) {
		this.stewardInstructorStaffNo = stewardInstructorStaffNo;
	}
	public String getStewardCheckType() {
		return stewardCheckType;
	}
	public void setStewardCheckType(String stewardCheckType) {
		this.stewardCheckType = stewardCheckType;
	}
	public String getStewardDeadheadOps() {
		return stewardDeadheadOps;
	}
	public void setStewardDeadheadOps(String stewardDeadheadOps) {
		this.stewardDeadheadOps = stewardDeadheadOps;
	}
	public String getStewardDeadheadOpsStaffNo() {
		return stewardDeadheadOpsStaffNo;
	}
	public void setStewardDeadheadOpsStaffNo(String stewardDeadheadOpsStaffNo) {
		this.stewardDeadheadOpsStaffNo = stewardDeadheadOpsStaffNo;
	}
	public String getStewardDeadheadOther() {
		return stewardDeadheadOther;
	}
	public void setStewardDeadheadOther(String stewardDeadheadOther) {
		this.stewardDeadheadOther = stewardDeadheadOther;
	}
	public String getStewardDeadheadOtherStaffNo() {
		return stewardDeadheadOtherStaffNo;
	}
	public void setStewardDeadheadOtherStaffNo(String stewardDeadheadOtherStaffNo) {
		this.stewardDeadheadOtherStaffNo = stewardDeadheadOtherStaffNo;
	}
	public String getJumpSeating() {
		return jumpSeating;
	}
	public void setJumpSeating(String jumpSeating) {
		this.jumpSeating = jumpSeating;
	}
	public String getJumpSeatingStaffNo() {
		return jumpSeatingStaffNo;
	}
	public void setJumpSeatingStaffNo(String jumpSeatingStaffNo) {
		this.jumpSeatingStaffNo = jumpSeatingStaffNo;
	}
	public String getCrwPilotInf() {
		return crwPilotInf;
	}
	public void setCrwPilotInf(String crwPilotInf) {
		this.crwPilotInf = crwPilotInf;
	}
	public String getCrwStewardInf() {
		return crwStewardInf;
	}
	public void setCrwStewardInf(String crwStewardInf) {
		this.crwStewardInf = crwStewardInf;
	}
	public String getStd() {
		return std;
	}
	public void setStd(String std) {
		this.std = std;
	}
	public String getSafer1StaffNo() {
		return safer1StaffNo;
	}
	public void setSafer1StaffNo(String safer1StaffNo) {
		this.safer1StaffNo = safer1StaffNo;
	}
	public String getCaptainRemark() {
		return captainRemark;
	}
	public void setCaptainRemark(String captainRemark) {
		this.captainRemark = captainRemark;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getCompanyId() {
		return companyId;
	}
	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}
	public String getSignatureDate() {
		return signatureDate;
	}
	public void setSignatureDate(String signatureDate) {
		this.signatureDate = signatureDate;
	}
	public String getSignatureImageUrl() {
		return signatureImageUrl;
	}
	public void setSignatureImageUrl(String signatureImageUrl) {
		this.signatureImageUrl = signatureImageUrl;
	}
	public String getArrTimes() {
		return arrTimes;
	}
	public void setArrTimes(String arrTimes) {
		this.arrTimes = arrTimes;
	}
	public String getDispatcher() {
		return dispatcher;
	}
	public void setDispatcher(String dispatcher) {
		this.dispatcher = dispatcher;
	}
	public String getEngineer() {
		return engineer;
	}
	public void setEngineer(String engineer) {
		this.engineer = engineer;
	}
	public String getModdt() {
		return moddt;
	}
	public void setModdt(String moddt) {
		this.moddt = moddt;
	}	
	
}
