package net.jdair.operassist.bizz.flightrisk.vo;

import com.hnair.opcnet.api.ods.ntm.NtmApprovedNotamRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 */
@Data
public class NtmApprovedNotamSearchVo extends NtmApprovedNotamRequest implements Serializable {

    @ApiModelProperty(value = "页码。如: 1")
    private Integer page;

    @ApiModelProperty(value = "每页数量。 如：10")
    private Integer pageSize;

    private List<String> itemAList;

}
