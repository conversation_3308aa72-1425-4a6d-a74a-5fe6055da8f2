package net.jdair.operassist.bizz.flightrisk.controller;

import com.alibaba.fastjson.JSONArray;
import com.hnair.opcnet.api.ods.flt.FltLoadSheetInfoRequest;
import com.hnair.opcnet.api.ods.flt.FltLoadSheetInfoResponse;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.common.esbapi.ESBService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Api(value = " com.hnair.opcnet.api.ods.flt.FltFlightApi 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/FltFlightApi")
public class FltFlightApiController {

    @Resource
    private ESBService esbService;

    @ApiOperation(value = "查询 fltFlightApi_getFltLoadSheetInfoByPage 数据", notes = "查询 fltFlightApi_getFltLoadSheetInfoByPage 数据")
    @RequestMapping(value = "/getFltLoadSheetInfoByPage")
    public ApiResponse fltFlightApi_getFltLoadSheetInfoByPage(@RequestParam Map<String, Object> options ,
                                                              ApiRequest apiRequest) {
        Integer pageIndex =0 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }
        log.info("ESB查询esbService.fltFlightApi_getFltLoadSheetInfoByPage接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse  apiResponse = esbService.fltFlightApi_getFltLoadSheetInfoByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询esbService.fltFlightApi_getFltLoadSheetInfoByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }

    /**
     * 查询舱单历史数据
     * @param loadRequest    参数
     * @return  结果
     */
    @ApiOperation(value = "查询 getFltLoadSheetHistoryByPage 数据", notes = "查询 getFltLoadSheetHistoryByPage 数据")
    @RequestMapping(value = "/getFltLoadSheetHistoryByPage")
    public FltLoadSheetInfoResponse getFltLoadSheetHistoryByPage(FltLoadSheetInfoRequest loadRequest) {
        return esbService.getFltLoadSheetHistoryByPage(loadRequest);
    }

}
