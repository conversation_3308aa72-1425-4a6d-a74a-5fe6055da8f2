package net.jdair.operassist.bizz.flightrisk.vo;

//import org.codehaus.jackson.map.annotate.JsonSerialize;
//import org.codehaus.jackson.map.annotate.JsonSerialize.Inclusion;

//@JsonSerialize(include = Inclusion.NON_NULL)
public class CommonResponse implements java.io.Serializable{
	
	private RespResult result ;
	private Object data ;
	

	public RespResult getResult() {
		return result;
	}
    public void setResult(RespResult result){
    	this.result=result;
    }
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	
}
