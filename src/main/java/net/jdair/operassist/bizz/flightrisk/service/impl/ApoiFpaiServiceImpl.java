package net.jdair.operassist.bizz.flightrisk.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnair.opcnet.api.complextype.PageParam;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.domain.ApoiFpai;
import net.jdair.operassist.bizz.flightrisk.mapper.ApoiFpaiMapper;
import net.jdair.operassist.bizz.flightrisk.service.ApoiFpaiService;
import net.jdair.operassist.common.esbapi.ESBService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class ApoiFpaiServiceImpl implements ApoiFpaiService {

    @Autowired
    private ApoiFpaiMapper apoiFpaiMapper;
    @Autowired
    private ESBService esbService;

    @Override
    public Date maxUpdateTime() {
        QueryWrapper<ApoiFpai> queryWrapper = new QueryWrapper<>();

        queryWrapper.select(" max(updatedTime) as updatedTime ");

        List<Map<String, Object>> map  = apoiFpaiMapper.selectMaps(queryWrapper);
        if(map.size()>0){
            Date    updatedTime =  MapUtil.getDate(map.get(0),"updatedTime");
            return  updatedTime;
        }else{
            return null;
        }
    }

    @Override
    public void loadEsbDataToLocalDb(String days) throws Exception {
        Long millis_start =  System.currentTimeMillis();
        //取出最大值
        Date date =  maxUpdateTime();
        ApiRequest apiRequest = new ApiRequest();
        String updatedTimeStart_str = "";
        if(date==null){
            //初始化数据
            updatedTimeStart_str  = DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.parseDate(DateUtil.today()),-Integer.valueOf(days)));


        }else{
            updatedTimeStart_str = DateUtil.formatDateTime(date);
        }

        apiRequest.setOption("updatedTimeStart",updatedTimeStart_str);
        ApiResponse response =  esbService.csdApi_findDesiFparByPage(apiRequest);
        Integer totalPages = response.getPageResult().getTotalPages();
        Integer pageIndex  = response.getPageResult().getPageIndex();
        PageParam pageParam = new PageParam();
        pageParam.setOrderBy("id");
        pageParam.setOrderDir("ASC");

        for(int page =1; page<= totalPages ; page++  ){
            pageParam.setPageIndex(page);
            apiRequest.setPageParam(pageParam);
            response =  esbService.csdApi_findDesiFparByPage(apiRequest);
            if(response!=null){
                if(response.getResult().getResultCode()==0){
                    List<Map<String, Object>>  list = response.getData();
                    for(Map<String, Object> m  :   list){
                        ApoiFpai apoiFpai = BeanUtil.mapToBean(m, ApoiFpai.class,true);
                        apoiFpaiMapper.deleteById(apoiFpai.getId());
                        apoiFpaiMapper.insert(apoiFpai);
                    }
                }else{
                    throw new  Exception("请求esb出错："+response.getResult().getResultMsg());
                }
            }
        }

        log.error("执行ApoiFpaiServiceImpl.loadEsbDataToLocalDb花费"+(System.currentTimeMillis()-millis_start));

    }
    @Override
    public Page<ApoiFpai> findApoiFpaiByPage_cache(Map<String, Object> options, Page page) {
        //需要特殊处理的字段
                Set<String> sss = new HashSet<>();

                sss.add("flightNo");
                sss.add("depapT");
                sss.add("arrapT");
                sss.add("depap");
                sss.add("arrap");
                sss.add("datopChn");
                sss.add("srcStd");
                sss.add("sobt");
                sss.add("deleteds");
                sss.add("companyCode");
                sss.add("sibt");

        QueryWrapper<ApoiFpai>  queryWrapper = new QueryWrapper<>();
        if(options!=null){

            options.forEach((m,v)->{
                if(  StrUtil.endWithIgnoreCase(m,"_like")  ){
                    String mm =  StrUtil.replace(m,"_like","");
                    queryWrapper.like(mm,v);
                }
                else{
                    if(CollectionUtil.contains(sss,m)){

                        queryWrapper.eq(m,v);
                    };
                }
            });


//            if(MapUtil.getStr(options,"datopChn")!=null){
//                Date date = MapUtil.getDate(options,"datopChn");
//
//                queryWrapper.eq("datopChn",date);
//            }

            if(MapUtil.getStr(options,"updatedTimeStart")!=null){
                Date date = MapUtil.getDate(options,"updatedTimeStart");

                queryWrapper.ge("updatedTime",date);
            }

            if(MapUtil.getStr(options,"updatedTimeEnd")!=null){
                Date date = MapUtil.getDate(options,"updatedTimeEnd");
                queryWrapper.le("updatedTime",date);
            }

            if(MapUtil.getStr(options,"sobtStart")!=null){
                String date = MapUtil.getStr(options,"sobtStart");
                queryWrapper.ge("sobt",date);
            }

            if(MapUtil.getStr(options,"sobtEnd")!=null){
                String date = MapUtil.getStr(options,"sobtEnd");
                queryWrapper.le("sobt",date);
            }


            if(MapUtil.getStr(options,"srcStdStart")!=null){
                String srcStd_str = MapUtil.getStr(options,"srcStdStart");
                queryWrapper.ge("srcStd",srcStd_str);
            }

            if(MapUtil.getStr(options,"srcStdEnd")!=null){
                String srcStd_str = MapUtil.getStr(options,"srcStdEnd");
                queryWrapper.le("srcStd",srcStd_str);
            }
        }

        return apoiFpaiMapper.selectPage( page , queryWrapper);

    }
}
