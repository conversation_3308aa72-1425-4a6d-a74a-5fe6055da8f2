package net.jdair.operassist.bizz.flightrisk.domain;


import cn.zhxu.bs.bean.DbField;
import cn.zhxu.bs.bean.SearchBean;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
//import java.sql.Timestamp;

//@EqualsAndHashCode(callSuper = true)
//@Accessors(chain = true)
@SearchBean( tables = "flt_odsRoster_api_dutyRoster_v2")
@Data
@TableName("flt_odsRoster_api_dutyRoster_v2")
@ApiModel(value = "DutyRosterV2机组排班计划对象", description = "DutyRosterV2机组排班计划对象")
public class DutyRosterV2 {
    @DbField(value = "id")
    @ApiModelProperty("ID")
    @TableField("id")
    @TableId(type = IdType.AUTO)
    private Long id;//ID
    //  一编号
    @ApiModelProperty("dutyRosterId")
    @TableField("dutyRosterId")
    @DbField(value = "dutyRosterId")
    private String dutyRosterId;
    //  机组类型
    @ApiModelProperty("crewTypeId")
    @TableField("crewTypeId")
    @DbField(value = "crewTypeId")
    private String crewTypeId;
    //    公司代码
    @ApiModelProperty("companyCode")
    @TableField("companyCode")
    @DbField(value = "companyCode")
    private String companyCode;
    //          航班日期
    @ApiModelProperty("flightDate")
    @TableField("flightDate")
    @DbField(value = "flightDate")
    private Date flightDate;
    // 航班号
    @ApiModelProperty("flightNo")
    @TableField("flightNo")
    @DbField(value = "flightNo")
    private String flightNo;
    //            飞机号
    @ApiModelProperty("acNo")
    @TableField("acNo")
    @DbField(value = "acNo")
    private String acNo;
    // IATA 机型
    @ApiModelProperty("aircraftType")
    @TableField("aircraftType")
    @DbField(value = "aircraftType")
    private String aircraftType;
    // 计划起飞时间
    @ApiModelProperty("departTime")
    @TableField("departTime")
    @DbField(value = "departTime")
    private Date departTime;
    //            计划到达时间
    @ApiModelProperty("arrivalDate")
    @TableField("arrivalDate")
    @DbField(value = "arrivalDate")
    private Date arrivalDate;
    //    计划起飞机场
    @ApiModelProperty("departStation")
    @TableField("departStation")
    @DbField(value = "departStation")
    private String departStation;
    //            计划到达机场
    @ApiModelProperty("arrivalStation")
    @TableField("arrivalStation")
    @DbField(value = "arrivalStation")
    private String arrivalStation;
    // 员工编号
    @ApiModelProperty("staffId")
    @TableField("staffId")
    @DbField(value = "staffId")

    private String staffId;
    //  内网帐号
    @ApiModelProperty("loginId")
    @TableField("loginId")
    @DbField(value = "loginId")
    private String loginId;
    // 员工姓名
    @ApiModelProperty("staffName")
    @TableField("staffName")
    @DbField(value = "staffName")
    private String staffName;
    //  级别名称
    @ApiModelProperty("rankSd")
    @TableField("rankSd")
    @DbField(value = "rankSd")
    private String rankSd;

    //    航段
    @ApiModelProperty("sector")
    @TableField("sector")
    @DbField(value = "sector")
    private String sector;
    //            航段的中文名称
    @ApiModelProperty("sectorNick")
    @TableField("sectorNick")
    @DbField(value = "sectorNick")
    private String sectorNick;
    //    同一个航班的机组ID
    @ApiModelProperty("fID")
    @TableField("fID")
    @DbField(value = "fID")
    private String fID;

    // 发布ID
    @ApiModelProperty("pid")
    @TableField("pid")
    @DbField(value = "pid")
    private String pid;

    //发布时间
    @ApiModelProperty("wpDate")
    @TableField("wpDate")
    @DbField(value = "wpDate")
    private Date wpDate;

    //修改时间
    @ApiModelProperty("modTime")
    @TableField("modTime")
    @DbField(value = "modTime")
    private Date modTime;
    //    级别
    @ApiModelProperty("rank")
    @TableField("rank")
    @DbField(value = "rank")
    private String rank;

    //  删除标志
    @ApiModelProperty("deleted")
    @TableField("deleted")
    @DbField(value = "deleted")
    private Integer deleted;

    //    预计起飞时间（北京）
    @ApiModelProperty("etd")
    @TableField("etd")
    @DbField(value = "etd")
    private Date etd;
    //  预计到达时间（北京）
    @ApiModelProperty("eta")
    @TableField("eta")
    @DbField(value = "eta")
    private Date eta;
    //  核酸合规
    @ApiModelProperty("natResult")
    @TableField("natResult")
    @DbField(value = "natResult")
    private Integer natResult;
    //            航班动态ID
    @ApiModelProperty("dutyId")
    @TableField("dutyId")
    @DbField(value = "dutyId")
    private Integer dutyId;

}
