package net.jdair.operassist.bizz.flightrisk.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 */
@Data
public class AfiProcessDetailSearchVo implements Serializable {

    @ApiModelProperty(value = "页码。如: 1")
    private Integer page;

    @ApiModelProperty(value = "每页数量。 如：10")
    private Integer pageSize;

    // 主表ID	busAircraftFaultInfoId	String	例如：g6Jqe756HOSDKHbVTGK
    private String busAircraftFaultInfoId;

    // ODS更新时间>=	updatedTimeStart	String	格式：yyyy-MM-dd HH:mm:ss 例如：2018-05-25 15:26:52
    private String updatedTimeStart;

    // ODS更新时间<=	updatedTimeEnd	String	格式：yyyy-MM-dd HH:mm:ss 例如：2018-05-25 15:26:52
    private String updatedTimeEnd;

    // 删除标识	deleteds	String	不传则默认返回deleted为0的记录，以逗号分隔多个取值，如0,1； 0代表未删除，1代表删除
    private String deleteds;

}
