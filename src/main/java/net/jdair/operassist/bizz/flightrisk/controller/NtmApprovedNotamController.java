package net.jdair.operassist.bizz.flightrisk.controller;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnair.opcnet.api.complextype.PageParam;
import com.hnair.opcnet.api.ods.ntm.*;
import com.hnair.opcnet.api.v2.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.vo.NtmApprovedNotamSearchVo;
import net.jdair.operassist.common.esbapi.ESBService;
import net.jdair.operassist.common.exception.GlobalException;
import net.jdair.operassist.common.page.TableDataInfo;
import net.jdair.operassist.common.response.CodeDefault;
import net.jdair.operassist.common.response.RestResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * <p>
 * 航行通告
 * </p>
 *
 */
@Api(value = "NtmApprovedNotam 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/NtmApprovedNotam")
public class NtmApprovedNotamController {

    @Resource
    private ESBService esbService;


    /**
     * 查询分页数据
     */
    @ApiOperation(value = "查询 NtmApprovedNotam 分页数据", notes = "查询 NtmApprovedNotam 分页数据")
    @PostMapping(value = "/list")
    public RestResponse<Page<NtmApprovedNotam>> listByPage(@RequestBody NtmApprovedNotamSearchVo param) {

        NtmApprovedNotamRequest apiRequest = new NtmApprovedNotamRequest();
        BeanUtils.copyProperties(param, apiRequest);
        if(param.getItemAList()!=null) {
            apiRequest.getItemAList().addAll(param.getItemAList());
        }
        PageParam pageParam = new PageParam();
        pageParam.setPageIndex(param.getPage());
        pageParam.setPageSize(param.getPageSize());
        apiRequest.setPageParam(pageParam);
        NtmApprovedNotamResponse apiResponse = esbService.getNtmApprovedNotam(apiRequest);
        if (apiResponse.getResult().getResultCode() == 1) {
            return RestResponse.exception(apiResponse.getResult().getResultMsg());
        }

        return RestResponse.ok(new TableDataInfo(apiResponse.getNtmApprovedNotams(), apiResponse.getPageResult().getTotalRecords()));
    }


    /**
     *  查询雪情通告接口
     */
    @ApiOperation(value = "查询 getNtmSnowApprovedNotam 分页数据", notes = "查询 getNtmSnowApprovedNotam 分页数据")
    @PostMapping(value = "/getNtmSnowApprovedNotam")
    public NtmSnowApprovedNotamResponse getNtmSnowApprovedNotam(@RequestBody NtmSnowApprovedNotamRequest ntmSnowApprovedNotamRequest) {

        try {
        log.info("ESB查询esbService.napsApi_findCompanyRoutesByPage  接口，请求：{}", JSONArray.toJSONString(ntmSnowApprovedNotamRequest));
        }catch(Exception e){
            log.error("",e);
        }
        long start = System.currentTimeMillis();

        NtmSnowApprovedNotamResponse apiResponse =
                esbService.ntmApprovedNotamApi_getNtmSnowApprovedNotam(ntmSnowApprovedNotamRequest);

        long end = System.currentTimeMillis();
        try {
            log.info("ESB查询esbService.napsApi_findCompanyRoutesByPage 接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        }catch(Exception e){
           log.error("",e);
        }

        return apiResponse;
    }

}
