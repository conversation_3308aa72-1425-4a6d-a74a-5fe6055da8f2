package net.jdair.operassist.bizz.flightrisk.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.jdair.operassist.bizz.flightrisk.domain.FltFocStandardInfo;
import net.jdair.operassist.bizz.flightrisk.vo.FltFocStandInfoSearchVo;

import java.util.List;

/**
 * <p>
 * foc航班动态信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-02
 */
public interface FltFocStandardInfoService {

    /**
     * 分页查询FltFocStandardInfo
     *
     * @param param     参数
     * @return 返回mybatis-plus的Page对象,其中records字段为符合条件的查询结果
     * <AUTHOR>
     * @since 2022-08-02
     */
    Page<FltFocStandardInfo> listFltFocStandardInfosByPage(FltFocStandInfoSearchVo param);

    /**
     * 航班状态：ARR 落地 NDR落地 ATA到达      时间范围：前半个小时时间范围内
     */
    List<FltFocStandardInfo> listMq();
}
