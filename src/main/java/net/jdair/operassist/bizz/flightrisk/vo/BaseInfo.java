package net.jdair.operassist.bizz.flightrisk.vo;

public class BaseInfo {
	private Integer mi;// 用户id
	private String us;
	private String ctoke;
	private String dv;
	private String ctype;
	private String lan;
	private String ov;
	private String dm;
	private String dn;
	private String lat;
	private String lng;
	private String cln;
	private String av;
	private String loginName;// 用户登录名
	private String password;// 密码 边沿 12345678
	private String loginType;// 1.短信随机密码登录 2.用户名登录

	public Integer getMi() {
		return mi;
	}

	public void setMi(Integer mi) {
		this.mi = mi;
	}

	public String getUs() {
		return us;
	}

	public void setUs(String us) {
		this.us = us;
	}

	public String getCtoke() {
		return ctoke;
	}

	public void setCtoke(String ctoke) {
		this.ctoke = ctoke;
	}

	public String getDv() {
		return dv;
	}

	public void setDv(String dv) {
		this.dv = dv;
	}

	public String getCtype() {
		return ctype;
	}

	public void setCtype(String ctype) {
		this.ctype = ctype;
	}

	public String getLan() {
		return lan;
	}

	public void setLan(String lan) {
		this.lan = lan;
	}

	public String getOv() {
		return ov;
	}

	public void setOv(String ov) {
		this.ov = ov;
	}

	public String getDm() {
		return dm;
	}

	public void setDm(String dm) {
		this.dm = dm;
	}

	public String getDn() {
		return dn;
	}

	public void setDn(String dn) {
		this.dn = dn;
	}

	public String getLat() {
		return lat;
	}

	public void setLat(String lat) {
		this.lat = lat;
	}

	public String getLng() {
		return lng;
	}

	public void setLng(String lng) {
		this.lng = lng;
	}

	public String getCln() {
		return cln;
	}

	public void setCln(String cln) {
		this.cln = cln;
	}

	public String getAv() {
		return av;
	}

	public void setAv(String av) {
		this.av = av;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getLoginType() {
		return loginType;
	}

	public void setLoginType(String loginType) {
		this.loginType = loginType;
	}
}
