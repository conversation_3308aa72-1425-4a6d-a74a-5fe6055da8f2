package net.jdair.operassist.bizz.flightrisk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.jdair.operassist.bizz.flightrisk.domain.ApoiFpdi;
import net.jdair.operassist.bizz.flightrisk.domain.DesiFpar;

import java.util.Date;
import java.util.Map;

public interface ApoiFpdiService {

    public Date maxUpdateTime();

    public void loadEsbDataToLocalDb(String days)throws Exception;

    Page<ApoiFpdi> findApoiFpdiByPage_cache(Map<String, Object> options, Page page);
}
