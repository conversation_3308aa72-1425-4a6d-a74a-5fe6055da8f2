package net.jdair.operassist.bizz.flightrisk.vo;

import java.util.List;
import java.util.Map;

public class FlightReportResp implements java.io.Serializable{
	private static final long serialVersionUID = 1L;
	
	private List<FlightReportBean> fltRepList;
	
	private List<Map<String, Object>> result;
	
	private Integer totalRecords;

	public Integer getTotalRecords() {
		return totalRecords;
	}

	public void setTotalRecords(Integer totalRecords) {
		this.totalRecords = totalRecords;
	}

	public List<Map<String, Object>> getResult() {
		return result;
	}

	public void setResult(List<Map<String, Object>> result) {
		this.result = result;
	}

	public List<FlightReportBean> getFltRepList() {
		return fltRepList;
	}

	public void setFltRepList(List<FlightReportBean> fltRepList) {
		this.fltRepList = fltRepList;
	}
	
}
