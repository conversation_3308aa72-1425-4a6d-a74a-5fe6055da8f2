package net.jdair.operassist.bizz.flightrisk.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel("分页获取航班协同时隙信息(FCTI)")
@TableName("flt_operassist_api_weatherMessages")
@Data
public class WeatherMessages {
    @ApiModelProperty("补充组")
    @JsonProperty("complementarity")
    private String complementarity;//补充组
    @ApiModelProperty("源系统Id")
    @JsonProperty("srcId")
    private Integer srcId;//源系统Id
    @ApiModelProperty("翻译后的天气")
    @JsonProperty("weatherInfoTxt")
    private String weatherInfoTxt;//翻译后的天气
    @ApiModelProperty("翻译后的云况")
    @JsonProperty("cloudInfoTxt")
    private String cloudInfoTxt;//翻译后的云况
    @ApiModelProperty("报文类型")
    @JsonProperty("messageType")
    private String messageType;//报文类型
    @ApiModelProperty("机场四字码")
    @JsonProperty("airportCode")
    private String airportCode;//机场四字码
    @ApiModelProperty("报文时间UTC时间")
    @JsonProperty("messageUtcTime")
    private Date messageUtcTime;//报文时间UTC时间
    @ApiModelProperty("风向")
    @JsonProperty("windFX")
    private String windFX;//风向
    @ApiModelProperty("风速")
    @JsonProperty("windFS")
    private String windFS;//风速
    @ApiModelProperty("风速")
    @JsonProperty("windFxV")
    private String windFxV;//风速
    @ApiModelProperty("阵风")
    @JsonProperty("windZF")
    private String windZF;//阵风
    @ApiModelProperty("能见度")
    @JsonProperty("visibility")
    private Integer visibility;//能见度
    @ApiModelProperty("天气现象")
    @JsonProperty("weatherStatus")
    private String weatherStatus;//天气现象
    @ApiModelProperty("云况")
    @JsonProperty("cloud")
    private String cloud;//云况
    @ApiModelProperty("温度")
    @JsonProperty("temperature")
    private Integer temperature;//温度
    @ApiModelProperty("温度")
    @JsonProperty("dewPoint")
    private Integer dewPoint;//温度
    @ApiModelProperty("修正海压A")
    @JsonProperty("airPressureA")
    private String airPressureA;//修正海压A
    @ApiModelProperty("修正海压Q")
    @JsonProperty("airPressureQ")
    private String airPressureQ;//修正海压Q
    @ApiModelProperty("跑道视程")
    @JsonProperty("rvr")
    private String rvr;//跑道视程
    @ApiModelProperty("报文内容")
    @JsonProperty("message")
    private String message;//报文内容
    @ApiModelProperty("开始时间")
    @JsonProperty("startUtcTime")
    private Date startUtcTime;//开始时间
    @ApiModelProperty("结束时间")
    @JsonProperty("endUtcTime")
    private Date endUtcTime;//结束时间
//    @ApiModelProperty("查询预况气象报文结果列表")
//    @JsonProperty("weatherForecastAnalysisMessages")
//    private List weatherForecastAnalysisMessages;//查询预况气象报文结果列表


}
