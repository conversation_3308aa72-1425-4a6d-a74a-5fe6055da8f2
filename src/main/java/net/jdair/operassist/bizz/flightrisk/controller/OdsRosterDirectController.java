package net.jdair.operassist.bizz.flightrisk.controller;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
//import cn.zhxu.bs.MapSearcher;
//import cn.zhxu.bs.SearchResult;
import cn.zhxu.bs.MapSearcher;
import cn.zhxu.bs.SearchResult;
import cn.zhxu.bs.operator.Between;
import cn.zhxu.bs.operator.GreaterEqual;
import cn.zhxu.bs.operator.GreaterThan;
import cn.zhxu.bs.operator.LessEqual;
import cn.zhxu.bs.util.MapUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnair.opcnet.api.complextype.PageParam;
import com.hnair.opcnet.api.complextype.PageResult;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.domain.DutyRosterV2;
import net.jdair.operassist.bizz.flightrisk.domain.OdsRoster;
import net.jdair.operassist.bizz.flightrisk.service.*;
import net.jdair.operassist.bizz.flightrisk.vo.*;
import net.jdair.operassist.common.esbapi.ESBService;
import net.jdair.operassist.common.exception.BaseException;
import net.jdair.operassist.common.page.TableDataInfo;
import net.jdair.operassist.common.response.RestResponse;
import net.jdair.operassist.common.response.esb.EsbApiResponse;
import net.jdair.operassist.common.response.esb.OdsRosterEsb;
import net.jdair.operassist.common.util.LocalDateUtils;
import net.jdair.operassist.common.util.OdsRouterUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Ods 新机组排班任务书V2 相关Api封装
 *   直接转发esb 不做 任何修改
 *   OdsRosterApi
 *   com.hnair.opcnet.api.ods.roster.OdsRosterApi
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/operassist/odsRoster")
public class OdsRosterDirectController {

    @Resource
    private ESBService esbService;
    @Resource
    private OdsRosterService odsRosterService;

    @Resource
    private DutyRosterV2Service dutyRosterV2Service;

    @Autowired
    private MapSearcher mapSearcher;

    @ApiOperation(
            value = "esb任务书接口接口转发com.hnair.opcnet.api.ods.roster.OdsRosterApi.findFlightReportV2",
            notes = "esb任务书接口接口转发 ")
    @RequestMapping(value = "findFlightReportV2" )
    public ApiResponse findFlightReportV2(@RequestParam Map<String, Object> options , ApiRequest apiRequest) {
        Integer pageIndex =0 ;
        try{
            if(apiRequest.getPageParam()!=null){
                pageIndex = apiRequest.getPageParam().getPageIndex();
            };
            apiRequest.getOptions().putAll(options);
        }catch(Exception e){
            log.error("获取分页参数出错",e);
        }
        log.info("同步航班动态数据查询接口(第{}页)，开始", pageIndex);
        //查询数据
        ApiResponse response = esbService.findFlightReportV2Direct(apiRequest);
        log.info("同步航班动态数据查询接口(第{}页)，结束", pageIndex);

        return response;
    }

    @ApiOperation(
            value = "esb任务书接口接口转发com.hnair.opcnet.api.ods.roster.OdsRosterApi.findFlightReportV2",
            notes = "esb任务书接口接口转发,兼容老接口平台 ")
     /***
      * 兼容json与老接口平台一致
      * ***/
    @RequestMapping(value = {"findFlightReportV2_old","searchFlightReport.json"} )
    public String findFlightReportV2_old(@RequestParam Map<String, Object> options ,
                                                       ApiRequest apiRequest,
                                                       FlightReportReq req,
                                                       HttpServletRequest r) {
//        Integer pageIndex =0 ;
//        try{
//            if(apiRequest.getPageParam()!=null){
//                pageIndex = apiRequest.getPageParam().getPageIndex();
//            };
//            apiRequest.getOptions().putAll(options);
//        }catch(Exception e){
//            log.error("获取分页参数出错",e);
//        }
//        log.info("同步航班动态数据查询接口(第{}页)，开始", pageIndex);

        if (req.getAi()==null){
                return toFailJson("34453", "请传入参数ai.cp或 ai.cc或ai.ct");
        }else{
            if(
                    StringUtils.isBlank(req.getAi().getCp()) ||
                    StringUtils.isBlank(req.getAi().getCc()) ||
                    StringUtils.isBlank(req.getAi().getCt())
            ){
                return toFailJson("34453", "请传入参数ai.cp或 ai.cc或ai.ct");
            }
        }

        ApiRequest request = reqFlightReport(req);
        //查询数据
        ApiResponse response = esbService.findFlightReportV2Direct(request);
        if (response != null && response.getData() != null && response.getData().size() > 0) {
            PageResult pageResult = response.getPageResult();
            List<Map<String, Object>> result = response.getData();
            //返回
            FlightReportResp apiResp = new FlightReportResp();
            List<FlightReportBean> res = respFlightReport(req, result);
            if (!req.getAi().getCc().equals("0")) {
                apiResp.setTotalRecords(pageResult.getTotalRecords());
                apiResp.setResult(result);
            }
            return toSuccess(apiResp);
        } else {
            //保存日志信息
            //flightLog.setResult("调用失败");
            //flightLogService.insertFlightLog(flightLog);
            return toFailJson("10002", "查询失败，数据为空");
        }

        // log.info("同步航班动态数据查询接口(第{}页)，结束", pageIndex);
        //  return response;
    }



    /**
     * 封装飞行任务书接口入参请求
     * @param req
     * @return
     */
    private ApiRequest reqFlightReport(FlightReportReq req){
        ApiRequest request = new ApiRequest();
        if(req.getFlightNos() != null && req.getFlightNos() != ""){
            request.getOptions().put("fltIds", req.getFlightNos());
        }
        if(req.getFlightDate() != null && req.getFlightDate() != ""){
            request.getOptions().put("fltDate", req.getFlightDate());
        }
        if(req.getAcNo()!= null && req.getAcNo() != ""){
            request.getOptions().put("ac", req.getAcNo());
        }
        if(req.getRoutes()!= null && req.getRoutes() != ""){
            request.getOptions().put("routes", req.getRoutes());
        }
        if(req.getAircraftType()!= null && req.getAircraftType() != ""){
            request.getOptions().put("accd", req.getAircraftType());
        }
        if(req.getDeleteds()!= null && req.getDeleteds().size() > 0){
            request.getOptions().put("deleteds", req.getDeleteds());
        }
        if(req.getDepartStation() != null && req.getDepartStation() != ""){
            request.getOptions().put("depStn", req.getDepartStation());
        }
        if(req.getArrivalStation()!= null && req.getArrivalStation() != ""){
            request.getOptions().put("arrStn", req.getArrivalStation());
        }
        if(req.getStdFirstTimeStart()!= null && req.getStdFirstTimeStart() != ""){
            request.getOptions().put("stdFirstTimeStart", req.getStdFirstTimeStart());
        }
        if(req.getStdFirstTimeEnd()!= null && req.getStdFirstTimeEnd() != ""){
            request.getOptions().put("stdFirstTimeEnd", req.getStdFirstTimeEnd());
        }
        if(req.getUpdatedTimeStart()!= null && req.getUpdatedTimeStart() != ""){
            //更新时间始
            request.getOptions().put("updatedTimeStart", req.getUpdatedTimeStart());
        }
        if(req.getUpdatedTimeEnd()!= null && req.getUpdatedTimeEnd() != ""){
            //更新时间止
            request.getOptions().put("updatedTimeEnd", req.getUpdatedTimeEnd());
        }
        if(req.getPageParam()!=null  ){
            PageParam pageParam2= new PageParam ();
            pageParam2.setPageIndex(req.getPageParam().getPageIndex()!=null ? req.getPageParam().getPageIndex():1);
            pageParam2.setPageSize(req.getPageParam().getPageSize()!=null ? req.getPageParam().getPageSize():20);
            request.setPageParam(pageParam2);
        }else{
            PageParam pageParam= new PageParam ();
            pageParam.setPageSize(20);
            pageParam.setPageIndex(1);
            request.setPageParam(pageParam);
        }
        return request;
    }

    /**
     * 封装飞行任务书接口返回
     * @param req
     * @param result
     * @return
     */
    List<FlightReportBean > respFlightReport(FlightReportReq req, List<Map<String, Object>> result){
        List<FlightReportBean > flightReportList = new ArrayList<FlightReportBean>();
        if(result!=null && result.size()>0 && req.getAi().getCc().equals("5")){
            //机组转单
            for(int i=0; i<result.size(); i++){
                FlightReportBean bean= new FlightReportBean();
                bean.setFlightDate((String)result.get(i).get("fltDate"));
                bean.setFlightNos((String)result.get(i).get("fltIds"));
                bean.setRoutes((String)result.get(i).get("routes"));
                bean.setMaintainStn((String)result.get(i).get("maintainStn"));
                bean.setCaptain((String)result.get(i).get("captain"));
                bean.setCaptainStaffNo((String)result.get(i).get("captainStaffNo"));
                bean.setPilotChecker((String)result.get(i).get("pilotChecker"));
                bean.setPilotCheckerStaffNo((String)result.get(i).get("pilotCheckerStaffNo"));
                bean.setInspectedPilot((String)result.get(i).get("inspectedPilot"));
                bean.setInspectedPilotStaffNo((String)result.get(i).get("inspectedPilotStaffNo"));
                bean.setSkipper1(String.valueOf(result.get(i).get("skipper1")));
                bean.setSkipper1StaffNo((String)result.get(i).get("skipper1StaffNo"));
                bean.setFirstVice1((String)result.get(i).get("firstVice1"));
                bean.setFirstVice1StaffNo((String)result.get(i).get("firstVice1StaffNo"));
                bean.setChiefStewardCaptain((String)result.get(i).get("chiefStewardCaptain"));
                bean.setChiefStewardCaptainStaffNo((String)result.get(i).get("chiefStewardCaptainStaffNo"));
                bean.setStewardCap1((String)result.get(i).get("stewardCap1"));
                bean.setStewardCap1StaffNo((String)result.get(i).get("stewardCap1StaffNo"));
                bean.setSafer1((String)result.get(i).get("safer1"));
                bean.setStewards((String)result.get(i).get("stewards"));
                bean.setStewardsStaffNo((String)result.get(i).get("stewardsStaffNo"));
                bean.setStewardChecker((String)result.get(i).get("stewardChecker"));
                bean.setStewardCheckerStaffNo((String)result.get(i).get("stewardCheckerStaffNo"));
                bean.setStewardInstructor((String)result.get(i).get("stewardInstructor"));
                bean.setStewardInstructorStaffNo((String)result.get(i).get("stewardInstructorStaffNo"));
                bean.setSafer1StaffNo((String)result.get(i).get("safer1StaffNo"));
                bean.setCompanyName((String)result.get(i).get("companyName"));
                flightReportList.add(bean);
            }
        }else if(req.getAi().getCc().equals("0")){
            return flightReportList;
        }
        //保存日志信息
        //flightLog.setResult("调用成功");
        //flightLogService.insertFlightLog(flightLog);
        return flightReportList;
    }


    public static String toSuccess(Object dataObj){
        RespResult r = new RespResult();
        r.setMessage("succ");
        r.setResultCode("1000");
        r.setViewMessage(ErrorCode.errorcodemap.get("1000"));
        CommonResponse c = new CommonResponse();
        c.setResult(r);
        c.setData(dataObj);
        return JSONUtil.toJsonStr(c);
    }


    public static String toFailJson(String errorcode,String defaultmsg){
        RespResult r = new RespResult();
        r.setMessage("error");
        r.setResultCode(errorcode);
        if(!StrUtil.isEmpty(defaultmsg)){
            r.setViewMessage(defaultmsg);
        }else{
            r.setViewMessage(ErrorCode.errorcodemap.get(errorcode));
        }
        CommonResponse c = new CommonResponse();
        c.setResult(r);
        return JSONUtil.toJsonStr(c);
    }


    @ApiOperation(
            value = "机组排班接口 com.hnair.opcnet.api.ods.roster.OdsRosterApi.getDutyRosterV2 ",
            notes = "机组排班接口 转发esb接口 ")
    @RequestMapping(value = "getDutyRosterV2" )
    public ApiResponse getDutyRosterV2(@RequestParam Map<String, Object> options , ApiRequest apiRequest) {
        Integer pageIndex = 0;
        try {
            if (apiRequest.getPageParam() != null) {
                pageIndex = apiRequest.getPageParam().getPageIndex();
            }
            ;
            apiRequest.getOptions().putAll(options);
        } catch (Exception e) {
            log.error("获取分页参数出错", e);
        }
        log.info("同步航班动态数据查询接口(第{}页)，开始", pageIndex);
        //查询数据
        ApiResponse response = esbService.odsRosterApi_getDutyRosterV2(apiRequest);
        log.info("同步航班动态数据查询接口(第{}页)，结束", pageIndex);

        return response;
    }


    @ApiOperation(
            value = "机组排班接口 从数据库中读取历史数据，不包含今天的数据 ",
            notes = "机组排班接口 从数据库中读取历史数据，不包含今天的数据 查询参数参考文档 https://bs.zhxu.cn/guide/latest/start.html#%E5%BC%80%E5%A7%8B%E6%A3%80%E7%B4%A2 ")
    @RequestMapping(value = "getDutyRosterV2_db" )
//    public ApiResponse  getDutyRosterV2_db(@RequestParam Map<String, Object> params, ApiRequest apiRequest) {
    public SearchResult<Map<String, Object>>  getDutyRosterV2_db(@RequestParam Map<String, Object> params) {
//        Integer pageIndex = 1;
//        try {
//            if (apiRequest.getPageParam() != null) {
//                pageIndex = apiRequest.getPageParam().getPageIndex();
//            }
//        } catch (Exception e) {
//            log.error("获取分页参数出错", e);
//        }
//        Page<DutyRosterV2> resultPage = new Page<>(pageIndex, 100);
//        log.info("同步航班动态数据查询接口(第{}页)，开始", pageIndex);
//        //查询数据
//        QueryWrapper<DutyRosterV2>  queryp = new QueryWrapper<>();
//        // dutyRosterV2Service.getDutyRosterV2_db(resultPage,queryp);
//
//        log.info("同步航班动态数据查询接口(第{}页)，结束", pageIndex);
//         return null;
          Date startDate = MapUtil.getDate(params,"startDate");
          Date endDate   = MapUtil.getDate(params,"endDate");


      Map m=  MapUtils.builder()
                .field(DutyRosterV2::getFlightDate, startDate,endDate).op(Between.class)      // age >  18
                .build();
        params.putAll(m);
        SearchResult<Map<String, Object>> searchResult   = mapSearcher.search(DutyRosterV2.class , params );
         return  searchResult;

}



    @ApiOperation(
            value = "机组排班接口 com.hnair.opcnet.api.ods.roster.OdsRosterApi.getDutyRosterV2 ",
            notes = "机组排班接口 转发esb接口 ")
    @RequestMapping(value = "loadEsbDataToLocalDb_dutyRosterV2" )
    public RestResponse<String> loadEsbDataToLocalDb_dutyRosterV2(@RequestParam(name = "date",required = false) String date){
          dutyRosterV2Service.loadEsbDataToLocalDb_dutyRosterV2( date);
          return RestResponse.ok("成功");
    };

    /**
     * 导出技术级别信息接口V2
     */
    @RequestMapping(value = "/exportBasicTechLevelV2")
    public ApiResponse exportBasicTechLevelV2(@RequestParam Map<String, Object> options , ApiRequest apiRequest){
        apiRequest.getOptions().putAll(options);
        return esbService.exportBasicTechLevelV2(apiRequest);
    }

    /**
     * 查询资格信息(带分页)
     */
    @RequestMapping(value = "/getQualificationInfoV2")
    public ApiResponse getQualificationInfoV2(@RequestParam Map<String, Object> options , ApiRequest apiRequest){
        apiRequest.getOptions().putAll(options);
        return esbService.getQualificationInfoV2(apiRequest);
    }
}
