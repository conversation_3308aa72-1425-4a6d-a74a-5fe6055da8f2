package net.jdair.operassist.bizz.flightrisk.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.domain.FltFocStandardInfo;
import net.jdair.operassist.bizz.flightrisk.domain.OdsRoster;
import net.jdair.operassist.bizz.flightrisk.service.FltFocStandardInfoService;
import net.jdair.operassist.bizz.flightrisk.vo.FltFocStandInfoSearchVo;
import net.jdair.operassist.common.page.TableDataInfo;
import net.jdair.operassist.common.response.RestResponse;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * <p>
 * foc航班动态信息表 前端控制器
 * </p>
 *
 */
@Api(value = "flt_foc_standard_info 服务接口")
@Slf4j
@RestController
@RequestMapping("/operassist/standardinfo")
public class FltFocStandardInfoController {

    @Resource
    private FltFocStandardInfoService fltFocStandardInfoService;

    /**
     * 查询分页数据
     */
    @ApiOperation(value = "查询 flt_foc_standard_info 分页数据", notes = "查询 flt_foc_standard_info 分页数据")
    @PostMapping(value = "/list")
    public RestResponse<Page<FltFocStandardInfo>> listByPage(@RequestBody FltFocStandInfoSearchVo param) {
           Page<FltFocStandardInfo> pageResult = fltFocStandardInfoService.listFltFocStandardInfosByPage(param);
        return RestResponse.ok(new TableDataInfo(pageResult.getRecords(), (int) pageResult.getTotal()));
    }

    /**
     * 查询推送航科院的数据
     * @return 推送数据
     */
    @GetMapping(value = "/listMq")
    public RestResponse<OdsRoster> listMq(){
        // 航班状态：ARR 落地 NDR落地 ATA到达      时间范围：前半个小时时间范围内
        List<FltFocStandardInfo> fltFocStandardInfos = fltFocStandardInfoService.listMq();
        return RestResponse.ok(fltFocStandardInfos);
    }

}
