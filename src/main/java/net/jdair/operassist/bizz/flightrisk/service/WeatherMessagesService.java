package net.jdair.operassist.bizz.flightrisk.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.jdair.operassist.bizz.flightrisk.domain.MNfmsFmalFcti;
import net.jdair.operassist.bizz.flightrisk.domain.WeatherMessages;

import java.util.Map;

public interface WeatherMessagesService {


    public void loadEsbDataToLocalDb(String days)throws Exception;

    Page<WeatherMessages> findWeatherMessagesByPage_cache(Map<String, Object> options, Page page);



}
