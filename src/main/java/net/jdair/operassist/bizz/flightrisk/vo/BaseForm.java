package net.jdair.operassist.bizz.flightrisk.vo;

//import com.hnatourism.b2b.common.vo.AccessInfo;
//import com.hnatourism.b2b.common.vo.BaseInfo;


public class BaseForm implements java.io.Serializable{ 
	private static final long serialVersionUID = 1L;
	
		private	BaseInfo    bi;
        private	AccessInfo  ai;
        
		public BaseInfo getBi() {
			return bi;
		}
		public void setBi(BaseInfo bi) {
			this.bi = bi;
		}
		public AccessInfo getAi() {
			return ai;
		}
		public void setAi(AccessInfo ai) {
			this.ai = ai;
		}
}
