package net.jdair.operassist.bizz.flightrisk.vo;

import java.util.HashMap;
import java.util.Map;

/**
 * 对外提供的所有错误码集合
 * <AUTHOR>
 *
 */
public class ErrorCode {
	
	public static final String  SYS_CODE_9999 = "9999"; //系统出错
	public static final String  SYS_CODE_1000 = "1000"; //成功
	public static final String  SYS_CODE_1001 = "1001"; //数据不能为空
	public static final String  SYS_CODE_1007 = "1007"; //包含非法字符
	public static final String  SYS_CODE_1002 = "1002"; //未查询到数据
	public static final String  SYS_CODE_1003 = "1003"; //无权限查询该数据
	public static final String  SYS_CODE_1004 = "1004"; //请求数据格式有错
	public static final String SYS_CODE_1005  = "1005";//time out!
	public static final String SYS_CODE_1006  = "1006";//数据保存失败
	public static final String SYS_CODE_1008  = "1008";//app版本过低
	public static final String SYS_CODE_1009  = "1009";//参数转化失败
	public static final String SYS_CODE_1010  = "1010";//缺少必要参数
	public static final String SYS_CODE_1011  = "1011";//参数无效
	public static final String SYS_CODE_1012  = "1012";//重复操作
	public static final Map<String,String>  errorcodemap=new HashMap<String,String>();
	static{
			errorcodemap.put("9999","系统出错");
			errorcodemap.put("1000","成功");
			errorcodemap.put("1001","数据不能为空");
			errorcodemap.put("1007","包含非法字符");
			errorcodemap.put("1002","未查询到数据");
			errorcodemap.put("1003","无权限查询该数据");
			errorcodemap.put("1004","请求数据格式有错");
			errorcodemap.put("1005","time out!");
			errorcodemap.put("1006","数据保存失败");
			errorcodemap.put("1008","app版本过低");
			errorcodemap.put("1009","参数转化失败");
			errorcodemap.put("1010","缺少必要参数");
			errorcodemap.put("1011","参数无效");
			errorcodemap.put("1012","重复操作");
			errorcodemap.put("1013","请发送验证码");
			errorcodemap.put("1014","手机号重复");
			errorcodemap.put("1015","密码");
			errorcodemap.put("1016","账户不存在");
			errorcodemap.put("1017","订单");
		
	}
}