package net.jdair.operassist.bizz.flightrisk.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import net.jdair.operassist.bizz.flightrisk.domain.OdsRoster;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class OdsRosterSearchVo extends OdsRoster {

    @ApiModelProperty(value = "页码。如: 1")
    private Integer page;

    @ApiModelProperty(value = "每页数量。 如：10")
    private Integer pageSize;

    private String depStn;

    private String arrStn;

}
