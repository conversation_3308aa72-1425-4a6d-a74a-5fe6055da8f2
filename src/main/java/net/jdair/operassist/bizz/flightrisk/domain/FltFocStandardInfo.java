package net.jdair.operassist.bizz.flightrisk.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * foc航班动态信息表
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "FltFocStandardInfo对象", description = "foc航班动态信息表")
public class FltFocStandardInfo extends Model<FltFocStandardInfo> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableField("id")
    private Long id;

    @ApiModelProperty(value = "长飞机号:B8982")
    @TableField("ac_long_no")
    private String acLongNo;

    @ApiModelProperty(value = "航班号:JD5577")
    @TableField("flight_no")
    private String flightNo;

    @ApiModelProperty(value = "起飞机场三字码:PKX")
    @TableField("dep_stn")
    private String depStn;

    @ApiModelProperty(value = "降落机场三字码:SYX")
    @TableField("arr_stn")
    private String arrStn;

    @ApiModelProperty(value = "起飞机场中文名:大兴")
    @TableField("dep_stn_cn")
    private String depStnCn;

    @ApiModelProperty(value = "降落机场中文名:凤凰")
    @TableField("arr_stn_cn")
    private String arrStnCn;

    @ApiModelProperty(value = "起飞机场城市名:北京")
    @TableField("dep_city")
    private String depCity;

    @ApiModelProperty(value = "降落机场城市名:三亚")
    @TableField("arr_city")
    private String arrCity;

    @ApiModelProperty(value = "计划离港时间（机场当地时间）")
    @TableField("std_local")
    private Date stdLocal;

    @ApiModelProperty(value = "计划到港时间（机场当地时间）")
    @TableField("sta_local")
    private Date staLocal;

    @ApiModelProperty(value = "预计离港时间（机场当地时间）")
    @TableField("etd_local")
    private Date etdLocal;

    @ApiModelProperty(value = "预计到港时间（机场当地时间）")
    @TableField("eta_local")
    private Date etaLocal;

    @ApiModelProperty(value = "实际离港时间（机场当地时间）")
    @TableField("atd_local")
    private Date atdLocal;

    @ApiModelProperty(value = "实际到港时间（机场当地时间）")
    @TableField("ata_local")
    private Date ataLocal;

    @ApiModelProperty(value = "计划离港时间（北京时间）")
    @TableField("std_chn")
    private Date stdChn;

    @ApiModelProperty(value = "计划到港时间（北京时间）")
    @TableField("sta_chn")
    private Date staChn;

    @ApiModelProperty(value = "预计离港时间（北京时间）")
    @TableField("etd_chn")
    private Date etdChn;

    @ApiModelProperty(value = "预计到港时间（北京时间）")
    @TableField("eta_chn")
    private Date etaChn;

    @ApiModelProperty(value = "实际离港时间（北京时间）")
    @TableField("atd_chn")
    private Date atdChn;

    @ApiModelProperty(value = "实际到港时间（北京时间）")
    @TableField("ata_chn")
    private Date ataChn;

    @ApiModelProperty(value = "机型:320")
    @TableField("orig_ac_type")
    private String origAcType;

    @ApiModelProperty(value = "机型:33D")
    @TableField("ac_type")
    private String acType;

    @ApiModelProperty(value = "航班类型 L-国内  I-国际")
    @TableField("flt_type")
    private String fltType;

    @ApiModelProperty(value = "航班状态 ARR 落地 NDR落地ATD推出ATA到达CNL取消DEL延误DEP起飞RTR返航SCH计划")
    private String status;

    @ApiModelProperty(value = "是否经停航班 1- 经停航班   0-非经停")
    @TableField("is_stop_over")
    private Integer isStopOver;

    @ApiModelProperty(value = "备降代码")
    @TableField("div_recode")
    private String divRecode;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;

    @ApiModelProperty(value = "航班日期（当地）")
    @TableField("datop_local")
    private Date datopLocal;
}
