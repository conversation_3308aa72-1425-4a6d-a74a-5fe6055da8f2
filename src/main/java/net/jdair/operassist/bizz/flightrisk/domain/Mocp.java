package net.jdair.operassist.bizz.flightrisk.domain;

import lombok.Data;

import java.util.Date;

/**
 * MOCP表
 */
@Data
public class Mocp {
    /**
     * 本地库主键
     */
    private Long mocpId;

    /**
     * ODS主键ID
     */
    private Long id;

    /**
     * 源主键
     */
    private Long srcId;

    /**
     * 父ID
     */
    private Long pid;

    /**
     * 故障状态(0:OPEN,1:CLOSE,2:作废)
     */
    private Integer failureState;

    /**
     * 故障放行状态(0:未完成,1:已完成,2:MEL/CDL放行,3:其他依据放行)
     */
    private String rleaseState;

    /**
     * 事件状态(1:未完成,2:保留OPEN,3:保留CLOSE,4:已完成,5:作废)
     */
    private String eventStatus;

    /**
     * 机号
     */
    private String machineNumber;

    /**
     * 机型
     */
    private String planeModel;

    /**
     * 航司
     */
    private String aviation;

    /**
     * 航站
     */
    private String termin;

    /**
     * 故障描述
     */
    private String faultDescription;

    /**
     * 故障描述附件
     */
    private String faultAttachment;

    /**
     * 发动机型号
     */
    private String engineModel;

    /**
     * APU型号
     */
    private String apuModel;

    /**
     * 飞机引进时间
     */
    private Long srcCreatedTime;

    /**
     * 放行时间
     */
    private Long releaseTime;

    /**
     * 故障操作时间
     */
    private Long faultStartTime;

    /**
     * 填报时间
     */
    private Long creationTime;

    /**
     * 修改时间
     */
    private Date srcUpdateTime;

    /**
     * ODS创建时间
     */
    private Date createdTime;

    /**
     * ODS更新时间
     */
    private Date updatedTime;

    /**
     * 保障阶段(0:航前,1:过站,2:航后,3:定检,4:停场)
     */
    private String guarantee;

    /**
     * 运行阶段(1:放行前,2:放行后...11:关车后)
     */
    private String functionInfo;

    /**
     * 故障填报人(0:机组,1:代理机务...4:其他)
     */
    private String reporter;

    /**
     * 专业类别(0:ME,1:AV,2:CA,3:发动机,4:结构)
     */
    private String professional;

    /**
     * 机械延误(0:否,1:是)
     */
    private Integer mechanical;

    /**
     * 机械不正常事件(0:否,1:是)
     */
    private Integer machineAbnormal;

    /**
     * 重复性故障(0:否,1:是)
     */
    private Integer repetitive;

    /**
     * 非计划停场(0:否,1:是)
     */
    private Integer unplanned;

    /**
     * 不安全事件(0:否,1:是)
     */
    private Integer unsafe;

    /**
     * 技术决策(0:否,1:是)
     */
    private Integer technical;

    /**
     * 运行调查(0:否,1:是)
     */
    private Integer runsurvey;

    /**
     * MCO(0:否,1:是)
     */
    private Integer mco;

    /**
     * AOG飞机(0:否,1:是)
     */
    private Integer aog;

    /**
     * SDR(0:否,1:是)
     */
    private Integer sdr;

    /**
     * 关联工作台(0:否,1:是)
     */
    private Integer isWorkbench;

    /**
     * 技术支援(0:关,1:开)
     */
    private Integer isSupport;

    /**
     * 维修控制(0:关,1:开)
     */
    private Integer isRepair;

    /**
     * AOG(0:关,1:开)
     */
    private Integer isAog;

    /**
     * 工作台编号
     */
    private String workbenchId;

    /**
     * 事件号
     */
    private String eventUmber;

    /**
     * 处理时长
     */
    private String processTime;

    /**
     * 故障开启/关闭人
     */
    private String faultOperator;

    /**
     * 故障分类(1-客舱CABIN,2-结构损伤DEFECT,3-系统SYSTEM,4-其他OTHER)
     */
    private String elbType;

    /**
     * 状态(1-待签署，2-已签署，9-作废)
     */
    private String elbSignStatus;

    /**
     * 处理方式(1-新建,2-M项...9-退单)
     */
    private String elbMode;

    /**
     * 来源类型(1-故障报告单,2-故障处理单,3-故障限制单)
     */
    private String elbFromType;

    /**
     * 来源ID
     */
    private String elbFromId;

    /**
     * 来源编号
     */
    private String elbFromNo;

    /**
     * 章节号(-分割)
     */
    private String elbAta;

    /**
     * 工时
     */
    private String elbHourNum;

    /**
     * 影响放行标志(TRUE-影响放行,FALSE-不影响)
     */
    private String elbInfluence;

    /**
     * 签署人角色
     */
    private String elbSignRole;

    /**
     * ELB状态
     */
    private String elbStatus;

    /**
     * MCO编号
     */
    private String elbMcoList;

    /**
     * 添加的数据类型源(1:MOCP,2:ELB)
     */
    private Integer isType;

    /**
     * ELB根级来源ID
     */
    private String rootId;

    /**
     * ELB根级来源类型(1:故障报告单,2:故障处理单,3:故障限制单)
     */
    private String rootType;

    /**
     * 运行限制(01:高高原,02:国际,03:客舱,04:货舱,05:座椅,06:系统,07:载重,08:航段数据,99:其他)
     */
    private String runRestrict;

    /**
     * 保留依据状态(0:故障排除,1:办理保留,2:作废,3:OPEN)
     */
    private String reserveState;

    /**
     * 保留依据章节
     */
    private String releaseBasis;

    /**
     * 座椅限制
     */
    private String seatRestriction;

    /**
     * 座椅禁用
     */
    private String seatDisable;

    /**
     * 执行M项(0:否,1:是)
     */
    private String termExecute;

    /**
     * 起飞前M项(0:否,1:是)
     */
    private String takeOffM;

    /**
     * APU限制(0:否,1:是)
     */
    private String apu;

    /**
     * 保留类型(0:A类,1:B类,2:C类,3:D类)
     */
    private String retentionType;

    /**
     * 是否删除(0:否,1:是)
     */
    private Integer isDel;

    /**
     * 删除标记(0:否,1:是)
     */
    private Boolean deleted;

    /**
     * 填报人
     */
    private String filledBy;

    /**
     * 修改人用户编号
     */
    private Long updateUid;

    /**
     * 是否有O项(0:否,1:是)
     */
    private String isO;

    /**
     * 本地库创建时间
     */
    private Date localCreateTime;

    /**
     * 本地库更新时间
     */
    private Date localUpdateTime;

    /**
     * 是否已发送预警邮件
     */
    private Boolean sendAlertEmail;
}