package net.jdair.operassist;

import com.alibaba.dubbo.config.spring.context.annotation.DubboComponentScan;
import com.hnair.opcnet.rpc.annotation.EnableOdsApi;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication
@EnableEurekaClient              // 启动 eureka 服务注册
@DubboComponentScan(basePackages= {"com.hnair.opcnet.api","net.jdair.operassist.bizz"})
@EnableFeignClients
@MapperScan(basePackages = {"net.jdair.operassist.bizz.**.mapper"})
@EnableOdsApi
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}