package net.jdair.operassist.common.page;


import com.baomidou.mybatisplus.annotation.TableField;
import org.apache.commons.lang3.StringUtils;

/**
 * 分页数据
 * 
 * <AUTHOR>
 */
public class PageDomain
{
    /** 当前记录起始索引 */
    @TableField(exist = false)
    private Integer pageNum;

    /** 每页显示记录数 */
    @TableField(exist = false)
    private Integer pageSize;

    /** 排序列 */
    @TableField(exist = false)
    private String orderByColumn;

    /** 排序的方向desc或者asc */
    @TableField(exist = false)
    private String isAsc = "asc";

    public String getOrderBy()
    {
        if (StringUtils.isEmpty(orderByColumn))
        {
            return "";
        }
        return StringUtils.upperCase(orderByColumn) + " " + isAsc;
    }

    public Integer getPageNum()
    {
        return pageNum;
    }

    public void setPageNum(Integer pageNum)
    {
        this.pageNum = pageNum;
    }

    public Integer getPageSize()
    {
        return pageSize;
    }

    public void setPageSize(Integer pageSize)
    {
        this.pageSize = pageSize;
    }

    public String getOrderByColumn()
    {
        return orderByColumn;
    }

    public void setOrderByColumn(String orderByColumn)
    {
        this.orderByColumn = orderByColumn;
    }

    public String getIsAsc()
    {
        return isAsc;
    }

    public void setIsAsc(String isAsc)
    {
        this.isAsc = isAsc;
    }
}
