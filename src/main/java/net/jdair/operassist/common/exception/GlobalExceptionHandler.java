
package net.jdair.operassist.common.exception;

import net.jdair.operassist.common.response.CodeDefault;
import net.jdair.operassist.common.response.RestResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理
 *
 */
@RestControllerAdvice
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 系统自定义全局异常
     *
     * @param req
     * @param e
     *
     * @return
     *
     * @throws Exception
     */
    @ExceptionHandler(value = GlobalException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, GlobalException e)
            throws Exception {
        RestResponse exception = RestResponse.exception(e.getCodeEnum());
        exception.setCustomMessage("统一异常处理："+e.getMessage());
        log.error("统一异常处理GlobalException: {}", exception, e);

        return exception;
    }

    /**
     * controller 参数转化时, 主要从这里捕获错误信息
     */
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, HttpMessageNotReadableException e)
            throws Exception {
        RestResponse exception = RestResponse.exception(CodeDefault.ILLEGAL_ARGUMENT);
        exception.setCustomMessage("统一异常处理："+e.getMessage());
        log.error("统一异常处理HttpMessageNotReadableException: {}", exception, e);

        return exception;
    }

    /**
     * 这个兜底
     *
     * @param req
     * @param e
     *
     * @return
     *
     * @throws Exception
     */
    @ExceptionHandler(value = RuntimeException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, RuntimeException e) throws Exception {
        RestResponse exception = RestResponse.exception(CodeDefault.INTERNAL_SERVER_ERROR);
        exception.setCustomMessage("operassist统一异常处理："+e.getMessage());
        log.error("统一异常处理RuntimeException: {}", exception, e);

        return exception;
    }
}
