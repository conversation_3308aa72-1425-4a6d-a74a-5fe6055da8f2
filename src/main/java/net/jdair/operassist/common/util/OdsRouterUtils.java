package net.jdair.operassist.common.util;

import net.jdair.operassist.bizz.flightrisk.domain.OdsRoster;
import net.jdair.operassist.common.response.esb.EsbApiResponse;
import net.jdair.operassist.common.response.esb.OdsRosterEsb;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * odsroster转换的工具类
 *
 * <AUTHOR>
 */
public class OdsRouterUtils {

    public static List<OdsRoster> convertToDbEntity(EsbApiResponse<OdsRosterEsb> esbResponse) {
        List<OdsRoster> result = new ArrayList<>();
        if (esbResponse.getData().size() <= 0) {
            return result;
        }
        for (OdsRosterEsb data : esbResponse.getData()) {
            OdsRoster odsRoster = new OdsRoster();
            BeanUtils.copyProperties(data, odsRoster);
            odsRoster.setAcNo(data.getAc());
            odsRoster.setGmtCreate(new Date());
            odsRoster.setGmtModified(new Date());
            result.add(odsRoster);
        }
        return result;
    }

}
