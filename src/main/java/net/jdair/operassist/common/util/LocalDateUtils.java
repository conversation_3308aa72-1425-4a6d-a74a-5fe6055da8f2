package net.jdair.operassist.common.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

/**
 * java8的日期工具类
 *
 * <AUTHOR>
 */
public class LocalDateUtils {

    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 将日期字符串转换为localdate对象
     *
     * @param date 日期
     * @return localdate
     */
    public static LocalDate parseDate(String date) {
        return LocalDate.parse(date, DateTimeFormatter.ofPattern(YYYY_MM_DD));
    }

    /**
     * 获取日期的前num天日期
     *
     * @param date 日期
     * @param num       天数
     * @return localdate
     */
    public static String minusDay(String date, long num) {
        LocalDate localDate = parseDate(date);
        return localDate.minusDays(num).format(DateTimeFormatter.ofPattern(LocalDateUtils.YYYY_MM_DD));
    }

    /**
     * 获取日期的后num天日期
     *
     * @param date 日期
     * @param num       天数
     * @return localdate
     */
    public static String plusDay(String date, long num) {
        LocalDate localDate = parseDate(date);
        return localDate.plusDays(num).format(DateTimeFormatter.ofPattern(LocalDateUtils.YYYY_MM_DD));
    }

    /**
     * 获取日期范围内所有日期的集合
     *
     * @param start 开始日期
     * @param end   结束日期
     * @return 日期集合
     */
    public static List<String> getBetweenDateList(String start, String end) {
        List<String> resultList = new ArrayList<>();
        LocalDate startDate = LocalDate.parse(start);
        LocalDate endDate = LocalDate.parse(end);
        //如果是同一天，返回该天
        if (startDate.isEqual(endDate)){
            resultList.add(startDate.toString());
            return resultList;
        }
        //判断两个日期之间的天数
        long distance = ChronoUnit.DAYS.between(startDate, endDate);
        if (distance < 1) {
            return resultList;
        }
        //遍历并返回结果
        Stream.iterate(startDate, d -> d.plusDays(1)).limit(distance + 1).forEach(f -> resultList.add(f.toString()));
        return resultList;
    }

    /**
     * 获取当前时间  格式：
     * @return  返回当前时间
     */
    public static String now(){
        LocalDateTime now = LocalDateTime.now();
        return now.format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
    }


}
