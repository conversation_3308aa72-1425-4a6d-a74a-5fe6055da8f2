package net.jdair.operassist.common.util;

import javax.servlet.http.HttpServletRequest;

/**
 * IP 客户端获取
 *
 */
public class ClientIpUtils {

    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() < 1 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        } else {
            String[] ips = ip.split(",");
            if (ips != null && ips.length > 1) {
                for (String s : ips) {
                    if ("unknown".equalsIgnoreCase(s)) {
                        ip = s;
                        break;
                    }
                }
            }
        }
        if (ip == null || ip.length() < 1 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() < 1 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
