package net.jdair.operassist.common.util;

import com.alibaba.fastjson.JSON;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

public class HttpRestTemplateUtil {

    public static ResponseEntity doPost(Object reqDTO,String url){

        RestTemplate client = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        HttpMethod method = HttpMethod.POST;
        // 以表单的方式提交
        headers.setContentType(MediaType.APPLICATION_JSON);
        //将请求头部和参数合成一个请求
        HttpEntity requestEntity = new HttpEntity<>(reqDTO, headers);
        //执行HTTP请求，将返回的结构使用ResultVO类格式化
        ResponseEntity response = client.exchange(url, method, requestEntity, String.class);

        return response;

    }

    public static void main(String[] args){
        RestTemplate restTemplate = new RestTemplate();
        Map<String, String> map = new HashMap<>();
        map.put("grant_type", "client_credential");
        map.put("appid", "wxaf9e9b1ede9e7e0b");
        map.put("secret", "7cf32c8a15af9c1d0b1e3b7a2124881f");
        ResponseEntity<String> responseEntity = restTemplate.getForEntity("https://api.weixin.qq.com/cgi-bin/token?grant_type={grant_type}&appid={appid}&secret={secret}", String.class, map);
        System.out.println(responseEntity.getBody());
        String accessToken = JSON.parseObject(responseEntity.getBody()).get("access_token").toString();
        System.out.println("accessToken = "+accessToken);

        Object errorCode = JSON.parseObject(responseEntity.getBody()).get("errcode");
        System.out.println("errorCode = "+errorCode);
//        System.out.println(new Date().getTime());
    }

}
