package net.jdair.operassist.common.util;

import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

/**
 * 生成秘钥工具
 *
 * @Date 2020/11/24 10:40
 * @Version 1.0
 **/
public class EncryptConfigUtil {
    /**
     * Jasypt生成加密结果
     *
     * @param password 配置文件中设定的加密盐值
     * @param value    加密值
     * @return
     */
    public static String encyptPwd(String password, String value) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        encryptor.setConfig(cryptor(password));
        String result = encryptor.encrypt(value);
        return result;
    }

    /**
     * 解密
     *
     * @param password 配置文件中设定的加密盐值
     * @param value    解密密文
     * @return
     */
    public static String decyptPwd(String password, String value) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        encryptor.setConfig(cryptor(password));
        String result = encryptor.decrypt(value);
        return result;
    }

    public static SimpleStringPBEConfig cryptor(String password) {
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword(password);
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setStringOutputType("base64");
        return config;
    }


    public static void main(String[] args) {

        // 加密
        String encPwd = encyptPwd("jasypt", "baseinfo");
        // 解密
        String decPwd = decyptPwd("jasypt", encPwd);


        // 加密
        String encPwd1 = encyptPwd("jasypt", "baseinfo@15UAT");
        // 解密
        String decPwd1 = decyptPwd("jasypt", encPwd1);
        System.out.println(encPwd);
        System.out.println(decPwd);

        System.out.println(encPwd1);
        System.out.println(decPwd1);

        System.out.println(decyptPwd("jasypt","AFe3XzKLC0jH3TLFZLJzbo83VdDOBQKI"));
        System.out.println(decyptPwd("jasypt","KVWmEFOjKq+eJMGC/lSMlm7mEHuuYSAOBPo9KyxMGAw="));

    }
}
