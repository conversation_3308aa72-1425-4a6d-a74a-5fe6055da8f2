package net.jdair.operassist.common.config;

import net.jdair.operassist.common.interceptor.DemoInterceptor;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.List;

@SpringBootConfiguration
public class InterceptorConfig implements WebMvcConfigurer {

    // 使拦截器提前加载，避免@Autowired无法获取到数据
    @Bean
    public DemoInterceptor getDemoInterceptor() {
        return new DemoInterceptor();
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        /*//注册自己的拦截器,并设置拦截路径，拦截多个可以全一个list集合*/
        //业务接口
        List<String> excludeList = new ArrayList<>();
        // excludeList.add("/demo/**");
        //组件接口
        excludeList.add("/swagger-resources/**");
        excludeList.add("/webjars/**");
        excludeList.add("/v2/**");
        excludeList.add("/error");
        excludeList.add("/csrf");
        excludeList.add("/");
        excludeList.add("/swagger-ui.html/**");
        excludeList.add("/doc.html/**");
        registry.addInterceptor(getDemoInterceptor()).addPathPatterns("/**").excludePathPatterns(excludeList);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**").addResourceLocations("classpath:/static/");
        registry.addResourceHandler("swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
        registry.addResourceHandler("doc.html").addResourceLocations("classpath:/META-INF/resources/");
    }
}
