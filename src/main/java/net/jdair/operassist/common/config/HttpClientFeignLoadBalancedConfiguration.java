package net.jdair.operassist.common.config;

import feign.httpclient.ApacheHttpClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * Feign默认使用JDK原生的HttpURLConnection作为http客户端，支持替换为ApacheHttpClient、OkHttp。
 * SpringCloud 2020.0.2及以后的版本，新增了对ApacheHttpClient5的支持。
 * 改为注入 ApacheHttpClient 实例
 *
 *  相比默认的HttpURLConnection，具有连接池的功能
 *  可以复用http连接，较少连接创建和销毁损耗
 *  相比OkHttp，ApacheHttpClient使用更加广泛，维护学习成本较低
 *  具有针对单个HOST/ROUTER配置连接上线的特性，可以做到资源简单隔离的用途
 *
 */
@Configuration
@ConditionalOnClass(ApacheHttpClient.class)
@ConditionalOnProperty(value = "feign.httpclient.enabled", matchIfMissing = true)
public class HttpClientFeignLoadBalancedConfiguration {

}
