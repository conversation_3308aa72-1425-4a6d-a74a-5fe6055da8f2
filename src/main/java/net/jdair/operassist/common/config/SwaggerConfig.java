package net.jdair.operassist.common.config;

import com.github.xiaoymin.swaggerbootstrapui.annotations.EnableSwaggerBootstrapUI;
import com.google.common.base.Predicates;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * @Date 2020/11/23 16:47
 * @Version 1.0
 **/
@Configuration
//@EnableSwagger2
//@EnableSwaggerBootstrapUI
// 在生产环境不开启
//@Profile({"dev", "sit"})
public class SwaggerConfig {
    @Bean
    public Docket adminApiConfig() {

        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("jd")
                .apiInfo(adminApiInfo())
                .select()
                .apis(Predicates.or(RequestHandlerSelectors.withClassAnnotation(RestController.class),
                        RequestHandlerSelectors.withClassAnnotation(Controller.class)
                )).apis(RequestHandlerSelectors.basePackage("net.jdair"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo adminApiInfo() {

        return new ApiInfoBuilder()
                .title("API文档")
                .description("本文档描述了系统微服务接口定义")
                .version("1.0")
                .contact(new Contact("", "http://localhost:8081", "<EMAIL>"))
                .build();
    }
}
