package net.jdair.operassist.common.esbapi;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.hnair.opcnet.api.ods.ac.AcAircraftApi;
import com.hnair.opcnet.api.ods.ap.*;
import com.hnair.opcnet.api.ods.crew.CrewPilotInfoApi;
import com.hnair.opcnet.api.ods.duty.HucDutyApi;
import com.hnair.opcnet.api.ods.efb.EfbFlightApi;
import com.hnair.opcnet.api.ods.flt.*;
import com.hnair.opcnet.api.ods.dsp.GetDspReleaseApi;
import com.hnair.opcnet.api.ods.eng.EngDeDeferApi;
import com.hnair.opcnet.api.ods.hr.HrOrgAndEmpApi;
import com.hnair.opcnet.api.ods.mocp.MocpApi;
import com.hnair.opcnet.api.ods.ntm.*;
import com.hnair.opcnet.api.ods.personalinfo.FlyingTrainingPlanRequest;
import com.hnair.opcnet.api.ods.personalinfo.FlyingTrainingPlanResponse;
import com.hnair.opcnet.api.ods.personalinfo.PersonalInfoApi;
import com.hnair.opcnet.api.ods.roster.OdsRosterApi;
import com.hnair.opcnet.api.ods.rst.*;
import com.hnair.opcnet.api.ods.wi.GetWeatherMessageRequest;
import com.hnair.opcnet.api.ods.wi.GetWeatherMessageResponse;
import com.hnair.opcnet.api.ods.wi.WheatherInfoApi;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.domain.Mocp;
import net.jdair.operassist.common.response.esb.*;
import org.springframework.stereotype.Service;
import com.hnair.opcnet.api.ods.de.CsdApi;
import com.hnair.opcnet.api.ods.mnfns.MNfnsApi;
import com.hnair.opcnet.api.ods.naps.NapsApi;
import com.hnair.opcnet.api.ods.psr.PassengerApi;
import com.hnair.opcnet.api.ods.flt.FltCateringPlanApi;
import com.hnair.opcnet.api.ods.ntm.NtmApprovedNotamApi;
import com.hnair.opcnet.api.ods.cif.CifApi;

/**
 * ESB接口方法封装类
 */
@Service
@Slf4j
public class ESBService {

    /**
     * 查询飞机故障描述、飞机故障描述进度明细
     */
    @Reference(version = "1.0.0", retries=2,timeout=300000, parameters = { "protocol", "dubbo" })
    private GetDspReleaseApi getDspReleaseApi;

    /**
     * 分页查询飞机维修 DD单信息
     */
    @Reference(version = "1.0.0", retries=2,timeout=300000, parameters = { "protocol", "dubbo" })
    private EngDeDeferApi engDeDeferApi;

    /**
     * 获取天气信息
     */
    @Reference(version = "1.0.0", retries=2,timeout=300000, parameters = { "protocol", "dubbo" })
    private WheatherInfoApi wheatherInfoApi;

    /**
     * 查询航行通告接口
     */


    /**
     * 新机组排版任务书V2接口
     */
    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private OdsRosterApi odsRosterApi;


    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private FltAirlineApi fltAirlineApi;


    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private AcAircraftApi acAircraftApi;


    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private FltFlightApi fltFlightApi;

    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private CsdApi   csdApi;

    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private MNfnsApi mNfnsApi;
    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private NapsApi napsApi;

    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private PassengerApi  passengerApi;

    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private FltCateringPlanApi  fltCateringPlanApi;

    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private NtmApprovedNotamApi  ntmApprovedNotamApi;

    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private CifApi   cifApi ;

    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private EfbFlightApi efbFlightApi;


    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private ApAirportFenxiApi apAirportFenxiApi;




    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private ApAirportApi apAirportApi;



    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private HucDutyApi hucDutyApi;

    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private HrOrgAndEmpApi hrOrgAndEmpApi;

    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private AdminIstrationRosterApi adminIstrationRosterApi;


    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private PersonalInfoApi personalInfoApi;


    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private GroundTrainRosterApi  groundTrainRosterApi;

    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private MocpApi mocpApi;

    @Reference(version = "1.0.0", retries = 2,timeout=300000, parameters = {"protocol", "dubbo"})
    private CrewPilotInfoApi crewPilotInfoApi;



    /**
     * 查询飞机故障描述方法。该方法是 V2版接口，封装输出对象中的 data 为具体业务类
     * @param apiRequest
     * @return
     */
    public EsbApiResponse<AircraftGaultInfo> getAircraftGaultInfoByPage(ApiRequest apiRequest) {
        log.info("ESB查询飞机故障描述接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = getDspReleaseApi.getAircraftGaultInfoByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询飞机故障描述接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);

        return new EsbApiResponse<AircraftGaultInfo>().wrapper(apiResponse, AircraftGaultInfo.class);
    }

    /**
     * 查询飞机故障描述进度明细接口方法。 该方法是 V2版接口，封装输出对象中的 data 为具体业务类
     * @param apiRequest
     * @return
     */
    public EsbApiResponse<AfiProcessDetail> getAfiProcessDetailByPage(ApiRequest apiRequest) {
        log.info("ESB查询飞机故障描述进度明细接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = getDspReleaseApi.getAfiProcessDetailByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询飞机故障描述进度明细接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return new EsbApiResponse<AfiProcessDetail>().wrapper(apiResponse, AfiProcessDetail.class);
    }

    /**
     * 查询飞机维修 DD单信息接口方法。 该方法是 V2版接口，封装输出对象中的 data 为具体业务类
     * @param apiRequest
     * @return
     */
    public EsbApiResponse<EngDeDefer> findEngDeDeferByPage(ApiRequest apiRequest) {
        log.info("ESB查询飞机维修 DD单信息接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = engDeDeferApi.findEngDeDeferByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询飞机维修 DD单信息接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return new EsbApiResponse<EngDeDefer>().wrapper(apiResponse, EngDeDefer.class);
    }

    /**
     * 查询飞机维修 DD单信息接口方法, 本地库存储。封装输出对象中的 data 为具体业务类
     * @param apiRequest
     * @return
     */
    public EsbApiResponse<Dd> findEngDeDeferByPageV2(ApiRequest apiRequest) {
        log.info("ESB查询DD单信息接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = engDeDeferApi.findEngDeDeferByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询DD单信息接口返回，耗时：{}ms", end - start);
        return new EsbApiResponse<Dd>().wrapper(apiResponse, Dd.class);
    }

    /**
     * 获取天气信息
     * @param apiRequest    请求参数
     * @return 结果
     */
    public GetWeatherMessageResponse getWeatherMessages(GetWeatherMessageRequest apiRequest){
        log.info("ESB获取天气信息接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        GetWeatherMessageResponse apiResponse = wheatherInfoApi.getWeatherMessages(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB获取天气信息接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }

    /**
     * 查询航行通告
     * @param apiRequest    请求参数
     * @return 结果
     */
    public NtmApprovedNotamResponse getNtmApprovedNotam(NtmApprovedNotamRequest apiRequest){
        log.info("ESB查询航行通告接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        NtmApprovedNotamResponse apiResponse = ntmApprovedNotamApi.getNtmApprovedNotam(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询航行通告接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }

    /**
     * 查询 新机组排版任务书接口
     *
     * @param apiRequest 请求
     */
    public EsbApiResponse<OdsRosterEsb> findFlightReportV2(ApiRequest apiRequest) {
        log.info("ESB查询新机组排版任务书接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = odsRosterApi.findFlightReportV2(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询新机组排版任务书接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return new EsbApiResponse<OdsRosterEsb>().wrapper(apiResponse, OdsRosterEsb.class);
    }

    public ApiResponse findFlightReportV2Direct(ApiRequest apiRequest) {
        log.info("ESB查询新机组排版任务书接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = odsRosterApi.findFlightReportV2(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询新机组排版任务书接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse ;
    }

    public FltPayloadResponse fltAirlineApi_getFltPayload(FltPayloadRequest apiRequest){
        log.info("ESB查询fltAirlineApi.getFltPayload接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        FltPayloadResponse apiResponse = fltAirlineApi.getFltPayload(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询fltAirlineApi.getFltPayload接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse ;

    }

    public ApiResponse fltFlightApi_getFltLoadSheetInfoByPage(ApiRequest apiRequest){
        log.info("ESB查询fltFlightApi.getFltLoadSheetInfoByPage接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = fltFlightApi.getFltLoadSheetInfoByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询fltFlightApi.getFltLoadSheetInfoByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse ;

    }

    public FltLoadSheetInfoResponse getFltLoadSheetHistoryByPage(FltLoadSheetInfoRequest loadRequest){
        log.info("ESB查询fltFlightApi.getFltLoadSheetHistoryByPage接口，请求：{}", JSONArray.toJSONString(loadRequest));
        long start = System.currentTimeMillis();
        FltLoadSheetInfoResponse loadResponse = fltFlightApi.getFltLoadSheetHistoryByPage(loadRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询fltFlightApi.getFltLoadSheetHistoryByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(loadResponse), end - start);
        return loadResponse ;

    }

    public ApiResponse acAircraftApi_getAcAircraftListByPage(ApiRequest apiRequest){
        log.info("ESB查询acAircraftApi.getAcAircraftListByPage接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = acAircraftApi.getAcAircraftListByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询acAircraftApi.getAcAircraftListByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse ;

    }


    public  ApiResponse  csdApi_findDesiFparByPage(ApiRequest apiRequest){
        log.info("ESB查询csdApi_findDesiFparByPage接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = csdApi.findDesiFparByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询csdApi_findDesiFparByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse ;
    }


    public ApiResponse   csdApi_findApoiFpdiByPage(ApiRequest apiRequest){
        log.info("ESB查询csdApi_findApoiFpdiByPage接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = csdApi.findApoiFpdiByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询csdApi_findApoiFpdiByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse ;
    }

    public ApiResponse   csdApi_findApoiFpaiByPage(ApiRequest apiRequest){
        log.info("ESB查询csdApi_findApoiFpaiByPage接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = csdApi.findApoiFpaiByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询csdApi_findApoiFpaiByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse ;
    }

    public ApiResponse   mNfnsApi_findMNfmsFmalFctiByPage(ApiRequest apiRequest){

        log.info("ESB查询mNfnsApi_findMNfmsFmalFctiByPage接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = mNfnsApi.findMNfmsFmalFctiByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询mNfnsApi_findMNfmsFmalFctiByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse ;

    }


    public ApiResponse   napsApi_findCompanyRoutesByPage(ApiRequest apiRequest){

        log.info("ESB查询napsApi_findCompanyRoutesByPage接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = napsApi.findCompanyRoutesByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询napsApi_findCompanyRoutesByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse ;

    }

    /**
     * 查询航班旅客汇总信息, 数据只有T+1 天
     * @param apiRequest
     * @return
     */
    public ApiResponse findPsrStat(ApiRequest apiRequest) {
        log.info("ESB查询航班旅客汇总信息，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = passengerApi.findPsrStat(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询航班旅客汇总信息，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }




    /**
     * 获得配餐执行计划
     * @param apiRequest
     * @return
     */
    public FltCateringPlanResponse fltCateringPlanApi_getCateringInfo(FltCateringPlanRequest apiRequest) {
        log.info("ESB查询获得配餐执行计划，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        FltCateringPlanResponse apiResponse = fltCateringPlanApi.getCateringInfo(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询获得配餐执行计划，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }

    /**
     *查询雪情通告接口
     * */
   public NtmSnowApprovedNotamResponse ntmApprovedNotamApi_getNtmSnowApprovedNotam(NtmSnowApprovedNotamRequest ntmSnowApprovedNotamRequest){
       log.info("ESB查询查询雪情通告接口，请求：{}", JSONArray.toJSONString(ntmSnowApprovedNotamRequest));
       long start = System.currentTimeMillis();
       NtmSnowApprovedNotamResponse ntmSnowApprovedNotamResponse =
               ntmApprovedNotamApi.getNtmSnowApprovedNotam(ntmSnowApprovedNotamRequest);
       long end = System.currentTimeMillis();
       log.info("ESB查询查询雪情通告接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(ntmSnowApprovedNotamResponse), end - start);
       return ntmSnowApprovedNotamResponse;
   }

    /**
     *查询雪情通告接口
     * */
    public ApiResponse cifApi_getMCifEffectrouteByPage(ApiRequest apiRequest){
            log.info("ESB查询查询雪情通告接口，请求：{}",JSONArray.toJSONString(apiRequest));
            long start = System.currentTimeMillis();
            ApiResponse apiResponse = cifApi.getMCifEffectrouteByPage(apiRequest);
            long end = System.currentTimeMillis();
            log.info("ESB查询查询雪情通告接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
            return apiResponse;
    }

    /**
     *   机组排班接口  ODS飞行计划（排班）的详细信息
     * */
    public ApiResponse odsRosterApi_getDutyRosterV2(ApiRequest apiRequest){
        log.info("机组排班接口，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = odsRosterApi.getDutyRosterV2(apiRequest);
        long end = System.currentTimeMillis();
        log.info("机组排班接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }


    /**
     * 查询efb机组信息
     * @param apiRequest    请求
     * @return 结果
     */
    public ApiResponse getEfbFlightLogLegsCrew(ApiRequest apiRequest){
        log.info("ESB查询航班getEfbFlightLogLegsCrew接口，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = efbFlightApi.getEfbFlightLogLegsCrew(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询航班getEfbFlightLogLegsCrew接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }

    /**
     *          查询机场分析信息
     * @param   airportFenxiInfoPageRequest    请求
     * @return 结果
     */
    public AirportFenxiInfoPageResponse getAirportFenxiInfoByPage(AirportFenxiInfoPageRequest  airportFenxiInfoPageRequest){
        log.info("ESB查询航班getAirportFenxiInfoByPage接口，请求：{}",JSONArray.toJSONString(airportFenxiInfoPageRequest));
        long start = System.currentTimeMillis();
        AirportFenxiInfoPageResponse apiResponse = apAirportFenxiApi.getAirportFenxiInfoByPage(airportFenxiInfoPageRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询航班getAirportFenxiInfoByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }

    public AirportInfoPageResponse getAirportInfoByPage(AirportInfoPageRequest airportInfoPageRequest){
        log.info("ESB查询航班getAirportInfoByPage接口，请求：{}",JSONArray.toJSONString(airportInfoPageRequest));
        long start = System.currentTimeMillis();
           AirportInfoPageResponse apiResponse = apAirportApi.getAirportInfoByPage(airportInfoPageRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询航班getAirportInfoByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }

    public ApiResponse hucDutyApi_getDutyInfoByPage(ApiRequest apiRequest){
            log.info("ESB查询航班getDutyInfoByPage接口，请求：{}",JSONArray.toJSONString(apiRequest));
            long start = System.currentTimeMillis();
                 ApiResponse apiResponse = hucDutyApi.getDutyInfoByPage(apiRequest);
            long end = System.currentTimeMillis();
            log.info("ESB查询航班getDutyInfoByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
            return apiResponse;
    }


    public ApiResponse hucDutyApi_getMHucRoleInfoByPage(ApiRequest apiRequest){
        log.info("ESB查询航班getMHucRoleInfoByPage接口，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = hucDutyApi.getMHucRoleInfoByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB查询航班getMHucRoleInfoByPage接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }


    /**
     *
     * 查询航线信息接口
     *
     **/
    public FltAirlineResponse fltAirlineApi_getFltAirline(FltAirlineRequest apiRequest){
        log.info("查询航线信息接口，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        FltAirlineResponse apiResponse = fltAirlineApi.getFltAirline(apiRequest);
        long end = System.currentTimeMillis();
        log.info("查询航线信息接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }


    /**
     *    特殊程序提醒查询(分页)
     *    com.hnair.opcnet.api.ods.flt.FltAirlineApi.getAirlineSpecialByPage
     **/
    public ApiResponse fltAirlineApi_getAirlineSpecialByPage(ApiRequest apiRequest){
        log.info("特殊程序提醒查询(分页)，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = fltAirlineApi.getAirlineSpecialByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("特殊程序提醒查询(分页)，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }


    /**
     *    分页查询航线信息接口
     *    com.hnair.opcnet.api.ods.flt.FltAirlineApi.getFltAirlineByPage
     **/
    public FltAirlinePageResponse fltAirlineApi_getFleAirlineByPage(FltAirlinePageRequest apiRequest){
        log.info("分页查询航线信息接口，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();

        FltAirlinePageResponse apiResponse = fltAirlineApi.getFltAirlineByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.info("分页查询航线信息接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }



    /**
     *    根据条件查找员工休假记录接口
     *    com.hnair.opcnet.api.ods.hr.HrOrgAndEmpApi.getEmpVocationRecord
     **/
    public ApiResponse hrOrgAndEmpApi_getEmpVocationRecord(ApiRequest apiRequest){
        log.info("分页查询航线信息接口，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = hrOrgAndEmpApi.getEmpVocationRecord(apiRequest);
        long end = System.currentTimeMillis();
        log.info("分页查询航线信息接口，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }


    /**
     *    ODS查询加机组计划
     *    com.hnair.opcnet.api.ods.roster.OdsRosterApi.findPositionRosterV2
     **/
    public ApiResponse odsRosterApi_findPositionRosterV2(ApiRequest apiRequest){
        log.info("分页查询ODS查询加机组计划，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = odsRosterApi.findPositionRosterV2(apiRequest);
        long end = System.currentTimeMillis();
        log.info("分页查询ODS查询加机组计划，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }


    /**
     *    ODS查询备份计划
     *    com.hnair.opcnet.api.ods.roster.OdsRosterApi.findStandByRosterV2
     **/
    public ApiResponse odsRosterApi_findStandByRosterV2(ApiRequest apiRequest){
        log.info("分页查询ODS查询备份计划，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = odsRosterApi.findStandByRosterV2(apiRequest);
        long end = System.currentTimeMillis();
        log.info("分页查询ODS查询备份计划，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }

    /**
     *    公务计划的数据
     *
     **/
    public GetAdminIstrationResponse adminIstrationRosterApi_getAdminIstration(GetAdminIstrationRequest apiRequest){
        log.info("分页查询公务计划的数据，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        GetAdminIstrationResponse apiResponse = adminIstrationRosterApi.getAdminIstration(apiRequest);
        long end = System.currentTimeMillis();
        log.info("分页查询公务计划的数据，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }

    /**
     * 空中训练
     * **/
    public FlyingTrainingPlanResponse personalInfoApi_getFlyingTrainingPlan(FlyingTrainingPlanRequest apiRequest){
            log.info("空中训练，请求：{}",JSONArray.toJSONString(apiRequest));
            long start = System.currentTimeMillis();
            FlyingTrainingPlanResponse apiResponse = personalInfoApi.getFlyingTrainingPlan(apiRequest);
            long end = System.currentTimeMillis();
            log.info("空中训练，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
            return apiResponse;
    }

    /**
     *    地面训练
     * **/
    public GetGroundTrainResponse groundTrainRosterApi_getGroundTrain(GetGroundTrainRequest apiRequest){
        log.info("地面训练，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        GetGroundTrainResponse apiResponse = groundTrainRosterApi.getGroundTrain(apiRequest);
        long end = System.currentTimeMillis();
        log.info("地面训练，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }


    /**
     *  MOCP数据监控(故障通报信息)
     **
     **/
    public EsbApiResponse<Mocp> findMocpViewInfoReportByPage(ApiRequest apiRequest) {
        log.debug("ESB分页查询MOCP数据监控(故障通报信息)接口，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = mocpApi.findMocpViewInfoReportByPage(apiRequest);
        long end = System.currentTimeMillis();
        log.debug("ESB分页查询MOCP数据监控(故障通报信息)接口返回，耗时：{}ms", end - start);
        return new EsbApiResponse<Mocp>().wrapper(apiResponse, Mocp.class);
    }

    /**
     * 导出技术级别信息接口V2
     */
    public ApiResponse exportBasicTechLevelV2(ApiRequest apiRequest) {
        log.info("技术级别，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = personalInfoApi.exportBasicTechLevelV2(apiRequest);
        long end = System.currentTimeMillis();
        log.info("技术级别，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }

    /**
     * 查询资格信息(带分页)
     */
    public ApiResponse getQualificationInfoV2(ApiRequest apiRequest) {
        log.info("资格信息，请求：{}",JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = crewPilotInfoApi.getQualificationInfoV2(apiRequest);
        long end = System.currentTimeMillis();
        log.info("资格信息，返回：{}，耗时：{}ms", JSONArray.toJSONString(apiResponse), end - start);
        return apiResponse;
    }
}
