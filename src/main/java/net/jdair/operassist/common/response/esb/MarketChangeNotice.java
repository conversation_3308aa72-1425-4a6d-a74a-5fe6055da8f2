package net.jdair.operassist.common.response.esb;

import lombok.Data;

import java.util.Date;

/**
 * 市场部-航班调整通知单 data对象
 */
@Data
public class MarketChangeNotice {

    private Date updatedTime;
    private String originalChangeInfo;
    private String flightNo;
    private String oldActype;
    private String actype;
    private String changeInfo;
    private String changeType;
    private Date createdTime;
    private String flightperiod;
    private Date updateTime;
    private ChangeTitle changeTitle;
    private long id;
    private String changeInfoDisplay;
    private String flightDatePeriod;


    @Data
    public static class ChangeTitle {
        private String companyCode;
        private String marketChangeNo;
        private Date updatedTime;
        private Date publishTime;
        private Date createdTime;
        private String adjustReason;
        private long id;
        private String title;
        private String loginIdentifier;
        private int publishStatus;
    }
}
