package net.jdair.operassist.common.response.esb;

import lombok.Data;

import java.util.Date;

/**
 * ods返回排班任务书数据
 *
 * <AUTHOR>
 */
@Data
public class OdsRosterEsb {

    /**
     * 航班日期 只包含首段的日期和时间,在还没有实际时间时，DATOP是计划时间，如有，那DATOP就是实际时间(北京时间)
     */
    private Date fltDate;

    /**
     * 航班号  数据可能为多个，以/分隔
     */
    private String fltIds;

    /**
     * 飞机号  数据可能为多个，以/分隔
     * 接口返回字段为ac，文档为acNo，以实际返回ac为准
     */
    private String ac;

    /**
     * 起飞日期
     */
    private String depTimes;

    /**
     * 航线  数据可能为多个，以/分隔
     */
    private String routes;

    /**
     * 报务教员
     */
    private String maintainStn;

    /**
     * 备注
     */
    private String cnRoutes;

    /**
     * 航班性质  数据可能为多个，以/分隔
     */
    private String stcs;

    /**
     * 飞行小时
     */
    private String flyTimes;

    /**
     * 机型  数据可能为多个，以/分隔
     */
    private String accd;

    /**
     * 机长  数据可能为多个，以@分隔
     */
    private String captain;

    /**
     * 机长员工编号  数据可能为多个，以@分隔
     */
    private String captainStaffNo;

    /**
     * 检查员 （飞行）  数据可能为多个，以/或、分隔
     */
    private String pilotChecker;
    /**
     * 检查员员工编号 （飞行）  数据可能为多个，以/分隔。有0值
     */
    private String pilotCheckerStaffNo;

    /**
     * 学员 （飞行）  数据可能为多个，以/分隔
     */
    private String inspectedPilot;

    /**
     * 学员员工编号（飞行）  数据可能为多个，以/分隔
     */
    private String inspectedPilotStaffNo;

    /**
     * 检查类别(飞行)
     */
    private String checkType;

    /**
     * 资深副驾驶 数据可能为多个，以@分隔
     */
    private String skipper1;

    /**
     * 资深副驾驶员工编号 数据可能为多个，以@分隔
     */
    private String skipper1StaffNo;

    /**
     * 资深副驾驶备注
     */
    private String skipper1Remark;

    /**
     * 副驾驶 数据可能为多个，以@分隔
     */
    private String firstVice1;

    /**
     * 副驾驶员工编号 数据可能为多个，以@分隔
     */
    private String firstVice1StaffNo;

    /**
     * 副驾驶备注
     */
    private String firstVice1Remark;

    /**
     * 非机组人员 （飞行) 数据可能为多个，以/分隔
     */
    private String accompany1;

    /**
     * 非机组人员ID（飞行) 数据可能为多个，以/分隔
     */
    private String accompany1StaffNo;

    /**
     * 执行航班加机组（飞行） 数据可能为多个，以/分隔
     */
    private String pilotDeadheadOps;

    /**
     * 执行航班加机组员工编号（飞行） 数据可能为多个，以/分隔
     */
    private String pilotDeadheadOpsStaffNo;

    /**
     * 非执行航班加机组（飞行） 数据可能为多个，以/分隔
     */
    private String pilotDeadheadOther;

    /**
     * 非执行航班加机组员工编号（飞行） 数据可能为多个，以/分隔
     */
    private String pilotDeadheadOtherStaffNo;

    /**
     * 乘务长 数据可能为多个，以/分隔
     */
    private String chiefStewardCaptain;

    /**
     * 乘务长员工编号 数据可能为多个，以/分隔
     */
    private String chiefStewardCaptainStaffNo;

    /**
     * 乘务长备注
     */
    private String chiefStewardCaptainRemark;

    /**
     * 乘务员 数据可能为多个，以/分隔
     */
    private String stewardCap1;

    /**
     * 乘务员员工编号 数据可能为多个，以/分隔
     */
    private String stewardCap1StaffNo;

    /**
     * 乘务员备注
     */
    private String stewardCap1Remark;

    /**
     * 安全员 数据可能为多个，以/分隔
     */
    private String safer1;

    /**
     * 乘务学员 数据可能为多个，以/分隔
     */
    private String stewards;

    /**
     * 乘务学员员工编号 数据可能为多个，以/分隔
     */
    private String stewardsStaffNo;

    /**
     * 检查员（乘务）	 数据可能为多个，以/分隔
     */
    private String stewardChecker;

    /**
     * 检查员员工编号（乘务） 数据可能为多个，以/分隔
     */
    private String stewardCheckerStaffNo;

    /**
     * 教员（乘务） 数据可能为多个，以/分隔
     */
    private String stewardInstructor;

    /**
     * 教员员工编号（乘务） 数据可能为多个，以/分隔
     */
    private String stewardInstructorStaffNo;

    /**
     * 检查类别（乘务） 目前无数据
     */
    private String stewardCheckType;

    /**
     * 执行航班加机组（乘务） 数据可能为多个，以/分隔
     */
    private String stewardDeadheadOps;

    /**
     * 执行航班加机组员工编号 （乘务） 数据可能为多个，以/分隔
     */
    private String stewardDeadheadOpsStaffNo;

    /**
     * 非执行航班加机组（乘务） 数据可能为多个，以/分隔
     */
    private String stewardDeadheadOther;

    /**
     * 非执行航班加机组员工编号（乘务）  数据可能为多个，以/分隔
     */
    private String stewardDeadheadOtherStaffNo;

    /**
     * 非机组加机组（乘务）  数据可能为多个，以/分隔
     */
    private String jumpSeating;

    /**
     * 非机组加机组员工编号（乘务）数据为0
     */
    private String jumpSeatingStaffNo;
    /**
     * 机组名单（飞行） 数据可能为多个，以;分隔
     */
    private String crwPilotInf;

    /**
     * 机组名单（乘务） 数据可能为多个，以;分隔
     */
    private String crwStewardInf;

    /**
     * 航班起飞日期
     */
    private Date std;

    /**
     * 安全员员工编号 数据可能为多个，以;分隔
     */
    private String safer1StaffNo;

    /**
     * 机长备注
     */
    private String captainRemark;

    /**
     * 银湖系统的公司编码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 银湖系统的公司ID
     */
    private String companyId;

    /**
     * 签发日期 日期格式
     */
    private Date signatureDate;

    /**
     * 签发人链接
     */
    private String signatureImageUrl;

    /**
     * 到达时间
     */
    private String arrTimes;

    /**
     * 签派员信息 高扬/曹少谦/施健/栾领路,TSN-WUH WUH-SYX  @张若骜, XIY-URC
     */
    private String dispatcher;

    /**
     * 机务跟机信息 郭茂林,PEK-XMN,过站放行@郭茂林,XMN-PEK,过站放行
     */
    private String engineer;

    /**
     * 第一航段计划起飞时间 格式yyyy-MM-dd HH:mm:ss
     */
    private Date stdFirst;

    /**
     * 巡航机长 数据可能为多个，以/或、分隔
     */
    private String cruisingCaptain;

    /**
     * 巡航机长员工编号 数据可能为多个，以/分隔。有0值
     */
    private String cruisingCaptains;

    /**
     * ODS更新时间
     */
    private Date updatedTime;

    /**
     * 其他加机组
     */
    private String jumpSeating1;

    /**
     * 新机组主键
     */
    private Integer flightId;

    /**
     * 航班动态ID
     */
    private Integer reportId;


}
