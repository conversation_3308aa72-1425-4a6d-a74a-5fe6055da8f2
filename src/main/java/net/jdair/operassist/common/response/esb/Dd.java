package net.jdair.operassist.common.response.esb;

import lombok.Data;

/**
 * 飞机维修 DD单信息 data对象
 */
@Data
public class Dd {

    // 航班号	flightNo	String
    private String flightNo;

    // 长机号	longNo	String
    private String longNo;

    // 机型	acType	String
    private String acType;

    // 保留依据	mel	String
    private String mel;

    // 航班限制依据以及原因的说明	document	String
    private String document;

    // 生成时间	creationDate	String
    private String creationDate;

    // 预计修复时间	expectedrepair	String
    private String expectedrepair;

    // 航班日期（UTC）	datop	String
    private String datop;

    // 航司三字码	station	String
    private String station;

    // 关闭日期	accomplDate	String
    private String accomplDate;

    // 更新时间	updateTime	String
    private String updateTime;

}
