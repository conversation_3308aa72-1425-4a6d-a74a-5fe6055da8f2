package net.jdair.operassist.common.response.esb;

import lombok.Data;

/**
 * 飞机故障描述进度明细 data对象
 */
@Data
public class AfiProcessDetail {

    // ODS主键ID	id	String
    private String id;

    // 主键ID	srcId	String
    private String srcId;

    // 故障主表ID	busAircraftFaultInfoId	String
    private String busAircraftFaultInfoId;

    // 故障时间	faultTime	String
    private String faultTime;

    // 故障描述	faultDetail	String
    private String faultDetail;

    // 显示顺序	faultIndex	String
    private String faultIndex;

    // 故障平台id	faultEventId	String
    private String faultEventId;

    // ODS创建时间	createdTime	Date
    private String createdTime;

    // JOB批次号，etl处理	batchJobNo	String
    private String batchJobNo;

    // ODS更新时间	updatedTime	Date
    private String updatedTime;

    // ODS删除标记，1删除，0有效	deleted	String
    private String deleted;

}
