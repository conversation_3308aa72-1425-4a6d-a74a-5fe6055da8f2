/*
 *
 */
package net.jdair.operassist.common.response;

import com.google.common.base.MoreObjects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 返回的错误代码, 返回前端用 int, 后端用枚举, 可以减少数据传输
 * 可以基于 code 的 name 进行国际化
 * 这里只列出系统默认的 code, 其他的 code, 应该按 CodePerson 这样进行区分
 * 建议划分方法, 使用 9 位数字 (一个 int 可以表示完全), 第一位固定是 1, 接下来 4 位划分模块, 最后四位划分具体的 errorCode;
 * 如: 10002xxxx, 表示用户相关模块错误
 */
@AllArgsConstructor
@Getter
public enum CodeDataPush implements CodeEnum {
    JYTC_NO_DATA(100030001, "没有符合条件的数据"),
    JYTC_SUBTIME_NULL(100030002, "发布日期不能为空"),
    JYTC_PAGE_SIZE_ERROR(100030003, "pageSize 最大不超500"),

    JYTC_ORDERDATA_SAVE_ERROR(100030004, "业务处理异常，请重试"),


    ;
    // 返回客户端的编码
    private final int code;

    // 默认消息
    private final String defaultMessage;

    public String getName() {
        return this.name();
    }

    public long getCode() {
        return this.code;
    }

    public String getDefaultMessage() {
        return this.defaultMessage;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("code", code)
                .add("defaultMessage", defaultMessage)
                .toString();
    }
}