package net.jdair.operassist.common.response.esb;

import lombok.Data;

/**
 * 飞机维修 DD单信息 data对象
 */
@Data
public class EngDeDefer {

    // 航班号	flightNo	String
    private String flightNo;

    // 长机号	longNo	String
    private String longNo;

    // 机型	acType	String
    private String acType;

    // 保留依据	mel	String
    private String mel;

    // 是否座椅限制,是标X，否空格	limitSeat	String
    private String limitSeat;

    // 限制座椅号,多个座椅号以英文逗号隔开3C,9F,70A	limitSeatNum	String
    private String limitSeatNum;

    // 是否座椅禁用,是标X，否空格	disableSeat	String
    private String disableSeat;

    // 禁用座椅号,多个座椅号以英文逗号隔开3C,9F,70A	disableSeatNum	String
    private String disableSeatNum;

    // 由于以上原因限制	limitDesc	String
    private String limitDesc;

    // 航班限制依据以及原因的说明	document	String
    private String document;

    // 生成时间	creationDate	String
    private String creationDate;

    // 预计修复时间	expectedrepair	String
    private String expectedrepair;

    // 航班日期（UTC）	datop	String
    private String datop;

    // 航班日期-北京	datopChn	String
    private String datopChn;

    // 航班日期-当地	datopLocal	String
    private String datopLocal;

    // 航司三字码	station	String
    private String station;

}
