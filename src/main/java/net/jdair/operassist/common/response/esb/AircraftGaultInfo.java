package net.jdair.operassist.common.response.esb;

import lombok.Data;

import java.util.Date;

/**
 * 飞机故障描述 data对象
 */
@Data
public class AircraftGaultInfo {
    // 主键	id	String
    private String id;

    // 源主键	srcId	String
    private String srcId;

    // 飞机号	aircraftNo	String
    private String aircraftNo;

    // 通告标题	title	String
    private String title;

    // 故障信息	faultInfo	String
    private String faultInfo;

    // 预计影响	expectedImpact	String
    private String expectedImpact;

    // 预计修复时间 , 年-月-日 时:分	expectedRepairTime	String
    private String expectedRepairTime;

    // 机务处理意见	dealOpinion1	String
    private String dealOpinion1;

    // 运控处理意见	dealOpinion2	String
    private String dealOpinion2;

    // 航材需求	materialRequire	String
    private String materialRequire;

    // 主流程状态 DRAFT 草稿状态 SENDED 已发送 UNCHECK 未审核 DEAL 运控、签派、航材子流程 FLOW_END 流程结束	flowStatus	String
    private String flowStatus;

    // 更新响应时间1	responseTime1	String
    private String responseTime1;

    // 更新响应时间2	responseTime2	String
    private String responseTime2;

    // 流程Key	processKey	String
    private String processKey;

    // 流程实例ID	processId	String
    private String processId;

    // 创建人	createUser	String
    private String createUser;

    // 创建时间	srcCreateTime	Date
    private String srcCreateTime;

    // 修改人	updateUser	String
    private String updateUser;

    // 修改时间	srcUpdateTime	Date
    private String srcUpdateTime;

    // 启动倒计时时间	startCountdownTime	String
    private String startCountdownTime;

    // 机场三字码	iataId	String
    private String iataId;

    // 影响地面保障 EFFECT Ground guarantee 0 不影响, 1影响	effectGg	String
    private String effectGg;

    // 影响上客 0 不影响, 1影响 	effectBoarding	String
    private String effectBoarding;

    // 维修放行 1 是 , 0 否	repairPass	String
    private String repairPass;

    // 是否存在MEL_CDL , 1 是存在 , 0不存在	melCdl	String
    private String melCdl;

    // 剩余时间提醒(单位分钟)	alarmMinute	String
    private String alarmMinute;

    // 是否航班放行 1 是放, 0是不行	flightPass	String
    private String flightPass;

    // 航班放行备注	flightPassDetail	String
    private String flightPassDetail;

    // 来源故障平台 1是，0否	fromTechnnic	String
    private String fromTechnnic;

    // 创建席位	createSeat	String
    private String createSeat;

    // 更新席位	updateSeat	String
    private String updateSeat;

    // 签派放行子流程状态 ，处理状态，子流程结束	qpStatus	String
    private String qpStatus;

    // 运控流程状态, 运控同意	ykStatus	String
    private String ykStatus;

    // 航材子流程状态 , 航材申请 , 航材紧急运输申请 参见航材流程表	hcStatus	String
    private String hcStatus;

    // NEW , FININSH	jwAlarmStatus	String
    private String jwAlarmStatus;

    // 事件ID	eventId	String
    private String eventId;

    // ODS创建时间	createdTime	Date
    private String createdTime;

    // JOB批次号，etl处理	batchJobNo	String
    private String batchJobNo;

    // ODS更新时间	updatedTime	Date
    private String updatedTime;

    // ODS删除标记，1删除，0有效	deleted	String
    private String deleted;

}
