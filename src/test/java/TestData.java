import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.hnair.opcnet.api.complextype.PageResult;
import com.hnair.opcnet.api.complextype.Result;
import com.hnair.opcnet.api.v2.ApiResponse;
import net.jdair.operassist.common.response.esb.EsbApiResponse;
import net.jdair.operassist.common.response.esb.StandardFocFlightInfo;
import org.junit.Test;

import java.util.*;

public class TestData {

    @Test
    public void esbResponse() {
//        ApiResponse testApiResponse = new ApiResponse();
//
//        List<Map<String, Object>> data = new ArrayList<>();
//
//        Map<String, Object> map = new HashMap<>();
//        map.put("tsTamp", new Date());
//        map.put("acOwn", "acOwn");
//        map.put("depCity", "depCity");
//
//        data.add(map);
//
//        testApiResponse.setResult(new Result());
//        testApiResponse.setPageResult(new PageResult());
//
//        EsbApiResponse<StandardFocFlightInfo> esbApiResponse = new EsbApiResponse<StandardFocFlightInfo>().wrapper(testApiResponse, StandardFocFlightInfo.class);
//
//        System.out.println(JSON.toJSONString(esbApiResponse));

        System.out.println( DateUtil.formatDateTime(DateUtil.parseDateTime("202202241500")) );

    }

}
