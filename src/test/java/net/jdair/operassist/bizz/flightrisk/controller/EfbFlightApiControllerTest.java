package net.jdair.operassist.bizz.flightrisk.controller;

import com.alibaba.fastjson.JSON;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.common.esbapi.ESBService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = net.jdair.operassist.Application.class)
@ActiveProfiles("dev")
@Slf4j
public class EfbFlightApiControllerTest {

    @Autowired
    private ESBService esbService;

    @Test
    public void getEfbFlightLogLegsCrew_Success() {
        ApiRequest request = new ApiRequest();
        Map<String, Object> options = new HashMap<>();
        options.put("keySplit", "true");
        options.put("sDate", "2024-03-04");
        options.put("eDate", "2024-03-21");
        options.put("flightNo", "JD5235");
        options.put("upDromeCode", "HAK");
        options.put("downDromeCode", "TYN");
        request.getOptions().putAll(options);
        ApiResponse apiResponse = esbService.getEfbFlightLogLegsCrew(request);
        System.out.println(JSON.toJSONString(apiResponse));
    }

}
