import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
//import com.route.common.core.page.TableDataInfo;
//import com.route.route.controller.AocriskAirportbadweatherMainController;
//import com.route.route.controller.AocriskFlightListController;
//import com.route.route.domain.AocriskStaffInfo;
//import com.route.route.domain.LoadcontrolFltPayloadScreen;
//import com.route.route.mapper.AocriskEgpwsMapper;
//import com.route.route.mapper.AocriskStaffInfoMapper;
//import com.route.route.service.*;
//import com.route.route.util.ApiJdairNetUtil;
import lombok.extern.slf4j.Slf4j;
import net.jdair.operassist.bizz.flightrisk.service.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes=net.jdair.operassist.Application.class)
@ActiveProfiles("dev") //这里
@Slf4j
public class MySpringBootTest {
@Autowired
    private DesiFparService desiFparService;
    @Test
    public void   test_maxUpdateTime(){
       Date date =    desiFparService.maxUpdateTime();
       log.error(DateUtil.formatDateTime(date));
    };

    @Test
    public void   test_loadEsbDataToLocalDb()throws Exception{
      //   desiFparService.loadEsbDataToLocalDb("10");
      //   apoiFpaiService.loadEsbDataToLocalDb("10");
         apoiFpdiService.loadEsbDataToLocalDb("10");
//         mNfmsFmalFctiService.loadEsbDataToLocalDb("10");
    }

    @Autowired
    private ApoiFpaiService apoiFpaiService ;
    @Autowired
    private ApoiFpdiService apoiFpdiService;
    @Autowired
    private MNfmsFmalFctiService mNfmsFmalFctiService;
    @Test
    public void   test_loadEsbDataToLocalDb_apoiFpai()throws Exception{
        apoiFpaiService.loadEsbDataToLocalDb("10");
    }
    @Autowired
    private DutyRosterV2Service dutyRosterV2Service;
    @Test
    public void   test_loadEsbDataToLocalDb_DutyRosterV2()throws Exception {
           dutyRosterV2Service.loadEsbDataToLocalDb_dutyRosterV2(null);
    }

}
