###
# For a quick start check out our HTTP Requests collection (Tools|HTTP Client|Open HTTP Requests Collection).
#
# Following HTTP Request Live Templates are available:
# * 'gtrp' and 'gtr' create a GET request with or without query parameters;
# * 'ptr' and 'ptrp' create a POST request with a simple or parameter-like body;
# * 'mptr' and 'fptr' create a POST request to submit a form with a text or file field (multipart/form-data);

POST  {{host}}/operassist/AcAircraftApi/getAcAircraftListByPage
Content-Type: application/x-www-form-urlencoded

acNoList=B-2157&pageParam.pageIndex=1
> {%
        //client.toString()   .log("321421");
        //client.log(JSON.stringify(response));
        //client.log(response.token);
        client.global.set("token123",response.body.token);
        client.global.set("salerNo",response.body.staffId);
        client.global.set("salerName",response.body.staffName);
        client.global.set("salerAcount",response.body.loginId);
%}

###
POST  {{host}}/operassist/FltFlightApi/getFltLoadSheetInfoByPage
Content-Type: application/x-www-form-urlencoded

flightNo=JD5358

###
POST  {{host}}/operassist/FltAirlineApi/getFltPayload
Content-Type: application/x-www-form-urlencoded

#depIcaoId=ZSJN&arrIcaoId=RJGG&payloadLb=41680&airlineAcType=B737-800(UQ)
updatedTimeStart=2021-09-01 00:00:00&updatedTimeEnd=2021-09-02 00:00:00

###
POST  {{host}}/operassist/csdApi/findDesiFparByPage
Content-Type: application/x-www-form-urlencoded

#updatedTimeStart=2021-09-01 00:00:00&pageParam.pageIndex=1&id=2&datopChn=2023-03-28&flightNo=PN6216
flightNo=JD5307&datopChn=2023-03-28

###
POST  {{host}}/operassist/csdApi/findDesiFparByPage_cache
Content-Type: application/x-www-form-urlencoded

#updatedTimeStart=2021-09-01 00:00:00&pageParam.pageIndex=2&flightNo_like=JD5617
#flightNo=JD5307&datopChn=2023-03-28&srcStdStart=
#srcStdStart=202202241500&srcStdEnd=202302241500
flightNo_like=JD53

###
POST  {{host}}/operassist/csdApi/findApoiFpdiByPage
Content-Type: application/x-www-form-urlencoded



###
POST  {{host}}/operassist/csdApi/findApoiFpdiByPage_cache
Content-Type: application/x-www-form-urlencoded

flightNo_like=JD53

###
POST  {{host}}/operassist/csdApi/findApoiFpaiByPage
Content-Type: application/x-www-form-urlencoded

###
POST  {{host}}/operassist/csdApi/findApoiFpaiByPage_cache
Content-Type: application/x-www-form-urlencoded

flightNo_like=JD53&datopChn=2023-04-01

###
POST  {{host}}/operassist/mNfnsApi/findMNfmsFmalFctiByPage
Content-Type: application/x-www-form-urlencoded



###
POST  {{host}}/operassist/mNfnsApi/findMNfmsFmalFctiByPage_cache
Content-Type: application/x-www-form-urlencoded

datopChn=2023-04-01


###
POST  {{host}}/operassist/companyRoutes/findCompanyRoutesByPage
Content-Type: application/x-www-form-urlencoded

organizationcode=JD&codeStartad=ZGKL&codeEndtad=ZJHK

###  转发飞行任务书
POST  {{host}}/operassist/odsRoster/findFlightReportV2
Content-Type: application/x-www-form-urlencoded

### 询航班旅客汇总信息（T+1）
POST  {{host}}/operassist/PassengerApi/findPsrStat
Content-Type: application/x-www-form-urlencoded

fltDate=2025-07-27

### 转发<获得配餐执行计划>接口
POST  {{host}}/operassist/FltCateringPlanApi/getCateringInfo
Content-Type: application/x-www-form-urlencoded

flightDateFrom=2025-06-24&flightDateTo=2025-07-24&flightNo=JD0496&depIataId=SVO&arrIataId=TAO

###  兼容查询任务书接口
POST  {{host}}/operassist/odsRoster/searchFlightReport.json?ai.cp=***********&ai.cc=1&ai.ct=1&flightDate=2023-06-22
Content-Type: application/x-www-form-urlencoded


###  接口平台返回任务书接口
POST  https://dsp.jdair.net/flightinterface/uss/json/flight/searchFlightReport.json?ai.cp=***********&ai.cc=1&ai.ct=1&flightDate=2023-06-22
Content-Type: application/x-www-form-urlencoded


###  返回雪情数据
POST  {{host}}/operassist/NtmApprovedNotam/getNtmSnowApprovedNotam
Content-Type: application/json

{
  "itemAList": ["ZBAD"],
  "pageSize":  1999
}



###  查询航行通告接口
POST  {{host}}/operassist/NtmApprovedNotam/list
Content-Type: application/json

{
  "prssdNotamPk": "123456",
  "itemAList": [],
  "pageSize":  1999
}


### 分页获取通告影响公司航路数据信息接口
POST  {{host}}//operassist/CifApi/getMCifEffectrouteByPage
Content-Type: application/x-www-form-urlencoded

notamfk=77387299


###
POST  {{host}}/operassist/odsRoster/getDutyRosterV2
Content-Type: application/x-www-form-urlencoded

startDate=2025-3-1&endDate=2025-7-2&staffId=1000844108



###
POST  {{host}}/operassist/odsRoster/getDutyRosterV2_db
Content-Type: application/x-www-form-urlencoded

#startDate=2024-3-9&endDate=2024-3-9&staffId=1001080482
staffId=1001080482&sort=id


###
POST  {{host}}/operassist/odsRoster/loadEsbDataToLocalDb_dutyRosterV2
Content-Type: application/x-www-form-urlencoded

###
POST  {{host}}/operassist/ApAirportFenxiApi/getAirportFenxiInfoByPage
Content-Type: application/x-www-form-urlencoded

pageParam.pageIndex=12


####
POST  {{host}}/operassist/ApAirportApi/getAirportInfoByPage
Content-Type: application/x-www-form-urlencoded

pageParam.pageIndex=2&pageParam.pageSize=10



####
POST  {{host}}/operassist/HucDutyApi/getDutyInfoByPage
Content-Type: application/x-www-form-urlencoded

dutyTime=2024-06-24&ad=da.sun


####
POST  {{host}}/operassist/FltAirlineApi/getFltAirline
Content-Type: application/x-www-form-urlencoded

depIataId=PEK
#depIcaoId=ZBAA


####
POST  {{host}}/operassist/FltAirlineApi/getAirlineSpecialByPage
Content-Type: application/x-www-form-urlencoded

sCityCode=ZBAA&pageParam.pageIndex=2



####
POST  {{host}}/operassist/FltAirlineApi/getFleAirlineByPage
Content-Type: application/x-www-form-urlencoded


updatedTimeStart=2024-09-26 00:00:00

#### ODS查询备份计划
POST  {{host}}/operassist/odsRoster/findStandByRosterV2
Content-Type: application/x-www-form-urlencoded

startDate=2025-03-29

####  空中训练（接口下线）
#POST  {{host}}/operassist/PersonalInfoApi/getFlyingTrainingPlan
#Content-Type: application/x-www-form-urlencoded

#startDate=2012-12-01&endDate=2024-12-01&pageParam.pageIndex=1&pageParam.pageSize=10


#### 地面训练
POST  {{host}}/operassist/groundTrainRosterApi/getGroundTrain
Content-Type: application/x-www-form-urlencoded

startDate=2024-06-18&endDate=2024-12-23&pageParam.pageIndex=1&pageParam.pageSize=10



####  加机组
POST  {{host}}/operassist/odsRoster/findPositionRosterV2
Content-Type: application/x-www-form-urlencoded

flightDate=2024-11-06&pageParam.pageIndex=1&pageParam.pageSize=10





####  培训计划
POST  {{host}}/operassist/adminIstrationRosterApi/getAdminIstration
Content-Type: application/x-www-form-urlencoded

startDate=2025-05-12&endDate=2025-05-12&pageParam.pageIndex=2&pageParam.pageSize=10
